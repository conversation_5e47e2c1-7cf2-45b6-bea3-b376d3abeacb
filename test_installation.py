#!/usr/bin/env python3
"""
Test script to verify the PDF Understanding Tool installation.

This script checks if all required dependencies are installed and
the basic functionality is working.
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported."""
    print("Testing imports...")

    # Critical imports (must work)
    critical_imports = [
        ("pdfplumber", "PDF text extraction"),
        ("fitz", "PyMuPDF - PDF processing"),
        ("pandas", "Data processing"),
        ("openpyxl", "Excel export"),
        ("fastapi", "Web framework"),
        ("uvicorn", "Web server"),
        ("python_dotenv", "Environment variables"),
        ("pathlib", "File path handling"),
    ]

    # Important imports (should work for full functionality)
    important_imports = [
        ("openai", "OpenAI API for entity extraction"),
        ("langchain", "LangChain framework for AI processing"),
        ("pytesseract", "Tesseract OCR"),
        ("PIL", "Pillow for image processing"),
    ]

    # Optional imports (nice to have)
    optional_imports = [
        ("boto3", "AWS Textract OCR"),
        ("google.cloud.vision", "Google Vision OCR"),
        ("sentence_transformers", "Advanced embeddings"),
        ("cv2", "OpenCV for image processing"),
        ("transformers", "Hugging Face transformers"),
    ]

    critical_failed = []
    important_failed = []
    optional_failed = []

    # Test critical imports
    print("\nCritical imports (required):")
    for module, description in critical_imports:
        try:
            if module == "python_dotenv":
                from dotenv import load_dotenv
            elif module == "fitz":
                import fitz
            else:
                __import__(module)
            print(f"✓ {module} - {description}")
        except ImportError as e:
            print(f"✗ {module} - {description} - ERROR: {e}")
            critical_failed.append(module)

    # Test important imports
    print("\nImportant imports (for full functionality):")
    for module, description in important_imports:
        try:
            if module == "PIL":
                import PIL
            else:
                __import__(module)
            print(f"✓ {module} - {description}")
        except ImportError as e:
            print(f"⚠ {module} - {description} - ERROR: {e}")
            important_failed.append(module)

    # Test optional imports
    print("\nOptional imports (enhanced features):")
    for module, description in optional_imports:
        try:
            if module == "google.cloud.vision":
                from google.cloud import vision
            elif module == "cv2":
                import cv2
            else:
                __import__(module)
            print(f"✓ {module} - {description}")
        except ImportError:
            print(f"- {module} - {description} - Not available")
            optional_failed.append(module)

    # Summary
    print(f"\nImport Summary:")
    print(f"Critical: {len(critical_imports) - len(critical_failed)}/{len(critical_imports)} ✓")
    print(f"Important: {len(important_imports) - len(important_failed)}/{len(important_imports)} ✓")
    print(f"Optional: {len(optional_imports) - len(optional_failed)}/{len(optional_imports)} ✓")

    if critical_failed:
        print(f"\n❌ Critical imports failed: {critical_failed}")
        print("These are required for basic functionality. Please install:")
        for module in critical_failed:
            print(f"  pip install {module}")
        return False

    if important_failed:
        print(f"\n⚠️  Important imports failed: {important_failed}")
        print("System will work but with limited functionality.")

    print("\n✅ Import test completed!")
    return True


def test_project_structure():
    """Test if the project structure is correct."""
    print("\nTesting project structure...")
    
    required_dirs = [
        "src",
        "src/agents",
        "src/models", 
        "src/processors",
        "src/exporters",
        "src/web",
        "src/web/templates",
        "uploads",
        "outputs",
        "templates",
        "temp"
    ]
    
    required_files = [
        "src/__init__.py",
        "src/main.py",
        "src/config.py",
        "requirements.txt",
        "README.md",
        ".env.example"
    ]
    
    all_good = True
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✓ {dir_path}/")
        else:
            print(f"✗ {dir_path}/ (missing)")
            all_good = False
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} (missing)")
            all_good = False
    
    if all_good:
        print("\nProject structure test completed successfully!")
    else:
        print("\n⚠ Some files/directories are missing!")
    
    return all_good


def test_basic_functionality():
    """Test basic functionality without requiring API keys."""
    print("\nTesting basic functionality...")
    
    try:
        # Test configuration loading
        sys.path.insert(0, str(Path.cwd()))
        from src.config import settings
        print("✓ Configuration loading")
        
        # Test model imports
        from src.models.entities import Machinery, Subcomponent, SparePart
        print("✓ Entity models")
        
        from src.models.document import PDFDocument, DocumentType
        print("✓ Document models")
        
        # Test basic model creation
        machinery = Machinery(name="Test Engine", type="Engine")
        print("✓ Machinery model creation")
        
        subcomp = Subcomponent(name="Test Bearing", parent_machinery="Test Engine")
        print("✓ Subcomponent model creation")
        
        spare_part = SparePart(name="Test Bolt", parent_subcomponent="Test Bearing")
        print("✓ Spare part model creation")
        
        print("\nBasic functionality test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False


def test_ocr_availability():
    """Test OCR functionality availability."""
    print("\nTesting OCR availability...")
    
    ocr_available = []
    
    # Test Tesseract
    try:
        import pytesseract
        # Try to get version (this will fail if tesseract is not installed)
        version = pytesseract.get_tesseract_version()
        print(f"✓ Tesseract OCR available (version: {version})")
        ocr_available.append("tesseract")
    except Exception as e:
        print(f"✗ Tesseract OCR not available: {e}")
    
    # Test AWS Textract
    try:
        import boto3
        print("✓ AWS Textract dependencies available (requires AWS credentials)")
        ocr_available.append("textract")
    except ImportError:
        print("✗ AWS Textract dependencies not available")
    
    # Test Google Vision
    try:
        from google.cloud import vision
        print("✓ Google Vision dependencies available (requires credentials)")
        ocr_available.append("vision")
    except ImportError:
        print("✗ Google Vision dependencies not available")
    
    if ocr_available:
        print(f"\nOCR providers available: {', '.join(ocr_available)}")
    else:
        print("\n⚠ No OCR providers available! Install Tesseract for basic OCR functionality.")
    
    return len(ocr_available) > 0


def main():
    """Run all tests."""
    print("PDF Understanding Tool - Installation Test")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # Run tests
    if test_imports():
        tests_passed += 1
    
    if test_project_structure():
        tests_passed += 1
    
    if test_basic_functionality():
        tests_passed += 1
    
    if test_ocr_availability():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! The installation looks good.")
        print("\nNext steps:")
        print("1. Copy .env.example to .env and add your API keys")
        print("2. Run: python run.py web")
        print("3. Open http://localhost:8000 in your browser")
    else:
        print("⚠ Some tests failed. Please check the output above.")
        print("\nCommon fixes:")
        print("- Run: pip install -r requirements.txt")
        print("- Install Tesseract OCR for your system")
        print("- Check that all files were created correctly")
    
    return tests_passed == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
