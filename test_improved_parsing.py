#!/usr/bin/env python3
"""Test the improved JSON parsing functionality."""

import sys
import json
import re
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path.cwd() / "src"))

from agents.entity_extractor import EntityExtractorAgent

def test_improved_parsing():
    """Test the improved JSON parsing with problematic responses."""
    
    # Create an instance to test the methods
    extractor = EntityExtractorAgent()
    
    # Test cases that were causing issues
    test_cases = [
        # Case 1: Trailing comma issue
        '''[
            {
                "name": "SPARE PART KIT",
                "part_number": "90T2-CRUDE",
                "subcomponent": "Maintenance",
                "material": null,
            }
        ]''',
        
        # Case 2: Mixed valid/invalid JSON
        '''[
            {
                "name": "Tank Cleaning Machine",
                "type": "Cleaning Equipment",
                "description": "Used for cleaning cargo tanks"
            },
            {
                "name": "High Pressure Pump",
                "type": "Pump",
                "description": "Provides high pressure water",
            }
        ]''',
        
        # Case 3: Markdown formatted JSON
        '''```json
        [
            {
                "name": "Main Engine",
                "type": "Engine",
                "description": "Primary propulsion system"
            }
        ]
        ```''',
        
        # Case 4: Malformed JSON that should trigger fallback
        '''The machinery found includes:
        - Tank Cleaning System
        - High Pressure Pump Unit
        - Control Valve Assembly
        
        These are the main components.'''
    ]
    
    print("Testing improved JSON parsing...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"Test Case {i}")
        print(f"Input: {test_case[:100]}...")
        
        try:
            result = extractor._parse_json_response(test_case)
            print(f"✅ Success! Parsed {len(result)} entities:")
            for j, entity in enumerate(result[:3]):  # Show first 3
                print(f"  {j+1}. {entity.get('name', 'Unknown')} - {entity.get('type', 'N/A')}")
            if len(result) > 3:
                print(f"  ... and {len(result) - 3} more")
                
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_improved_parsing()
