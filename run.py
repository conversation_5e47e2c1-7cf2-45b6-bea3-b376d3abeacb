#!/usr/bin/env python3
"""
Quick start script for the PDF Understanding Tool.

This script provides an easy way to run the application without
having to remember the full module path.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import and run the main application
try:
    from src.main import main
    
    if __name__ == "__main__":
        # If no arguments provided, default to web interface
        if len(sys.argv) == 1:
            sys.argv.append("web")
        
        main()
        
except ImportError as e:
    print(f"Error importing application: {e}")
    print("Make sure you have installed all dependencies:")
    print("pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"Error running application: {e}")
    sys.exit(1)
