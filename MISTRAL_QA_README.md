# Mistral OCR & Document QA Integration

This document describes the new Mistral AI-powered OCR and Document Question & Answer capabilities integrated into the PDF Understanding Tool.

## Overview

The system now includes advanced document understanding capabilities using Mistral's Pixtral vision model, which provides:

- **Enhanced OCR**: Superior text extraction from document images
- **Document QA**: Natural language question answering about document content
- **Structured Data Extraction**: Intelligent extraction of spare parts, maintenance jobs, and document structure
- **Multi-modal Understanding**: Combined text and image analysis for better accuracy

## Features

### 1. Document Question & Answer
- Upload document images (JPG, PNG, PDF)
- Ask natural language questions about the content
- Get detailed answers with supporting evidence and confidence levels
- Interactive web interface at `/qa`

### 2. Enhanced OCR Processing
- Mistral Pixtral model for superior text recognition
- Better handling of complex layouts and technical documents
- Structured text extraction with sections and tables
- High confidence scores for extracted content

### 3. Intelligent Data Extraction
- **Spare Parts Extraction**: Automatically identify and extract spare parts lists with:
  - Position numbers
  - Quantities
  - Part numbers
  - Descriptions
  - Equipment information
  
- **Maintenance Jobs Extraction**: Extract maintenance schedules including:
  - Job types (Inspection, Overhaul, Service)
  - Frequencies and intervals
  - Detailed task descriptions
  - Equipment associations

- **Document Structure Analysis**: Comprehensive document analysis including:
  - Document type identification
  - Section breakdown
  - Table detection and analysis
  - Key entity extraction
  - Quality assessment

## Configuration

### Environment Variables

Add the following to your `.env` file:

```bash
# Mistral AI Configuration
MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_MODEL=pixtral-12b-2409

# OCR Provider (optional - can use mistral for OCR)
OCR_PROVIDER=mistral
```

### API Key Setup

1. Get your Mistral API key from [Mistral AI Console](https://console.mistral.ai/)
2. Add it to your `.env` file
3. Restart the application

## Usage

### Web Interface

1. **Navigate to Document QA**: Click "Document QA" in the navigation menu
2. **Upload Document**: Select and upload a document image or PDF
3. **Ask Questions**: Type natural language questions about the document
4. **Quick Analysis**: Use quick action buttons for:
   - Extract Spare Parts
   - Extract Maintenance Jobs
   - Analyze Document Structure

### API Endpoints

#### Upload Document for QA
```http
POST /qa/upload
Content-Type: multipart/form-data

file: [document image file]
```

#### Ask Question
```http
POST /qa/question
Content-Type: application/x-www-form-urlencoded

file_id: [uploaded file ID]
question: [your question about the document]
```

#### Extract Spare Parts
```http
POST /qa/extract-spare-parts
Content-Type: application/x-www-form-urlencoded

file_id: [uploaded file ID]
```

#### Extract Maintenance Jobs
```http
POST /qa/extract-maintenance
Content-Type: application/x-www-form-urlencoded

file_id: [uploaded file ID]
```

#### Analyze Document Structure
```http
POST /qa/analyze-structure
Content-Type: application/x-www-form-urlencoded

file_id: [uploaded file ID]
```

### Python API

```python
from src.agents.document_qa_agent import DocumentQAAgent
from pathlib import Path

# Initialize QA agent
qa_agent = DocumentQAAgent()

# Answer questions about a document
result = qa_agent.answer_question(
    image_path=Path("document.jpg"),
    question="What spare parts are listed in this document?"
)

# Extract spare parts
spare_parts = qa_agent.extract_spare_parts_with_qa(
    image_path=Path("document.jpg")
)

# Extract maintenance jobs
maintenance = qa_agent.extract_maintenance_jobs_with_qa(
    image_path=Path("document.jpg")
)

# Analyze document structure
structure = qa_agent.analyze_document_structure(
    image_path=Path("document.jpg")
)
```

## Integration with Existing System

### Processing Coordinator Enhancement

The `ProcessingCoordinator` now includes QA agent capabilities:

```python
from src.agents.processing_coordinator import ProcessingCoordinator

coordinator = ProcessingCoordinator()

# Process document with QA agent
result = coordinator.process_with_qa_agent(
    image_path=Path("document.jpg")
)
```

### OCR Provider Selection

You can now use Mistral as an OCR provider:

```python
from src.processors.ocr_processor import get_ocr_processor

# Get Mistral OCR processor
ocr = get_ocr_processor("mistral")

# Extract text with high accuracy
text, confidence = ocr.extract_text(image_path)

# Get structured extraction
structured_data = ocr.extract_text_with_boxes(image_path)
```

## Benefits Over Traditional OCR

1. **Better Accuracy**: Mistral's vision model provides superior text recognition
2. **Context Understanding**: Understands document structure and relationships
3. **Natural Language Interface**: Ask questions in plain English
4. **Structured Output**: Automatically formats extracted data
5. **Multi-modal Analysis**: Combines visual and textual understanding
6. **Domain Awareness**: Better handling of technical maritime documents

## Example Use Cases

### 1. Spare Parts Identification
```
Question: "What is the part number for the shaft in position 1?"
Answer: "The part number for the shaft in position 1 is 30064."
```

### 2. Maintenance Schedule Queries
```
Question: "How often should the engine be inspected?"
Answer: "According to the maintenance schedule, the engine should be inspected every 12 months or 500 operating hours, whichever comes first."
```

### 3. Technical Specifications
```
Question: "What are the dimensions of the heat exchanger?"
Answer: "The heat exchanger has dimensions of 1200mm x 800mm x 600mm as specified in section 3.2."
```

## Troubleshooting

### Common Issues

1. **QA Service Unavailable**: Check that `MISTRAL_API_KEY` is set correctly
2. **Import Errors**: Ensure `mistralai>=0.4.0` is installed
3. **API Rate Limits**: Mistral API has rate limits - implement retry logic if needed
4. **Large Files**: Very large images may need to be resized before processing

### Error Messages

- `"QA service not available"`: Mistral API key not configured
- `"File not found"`: Uploaded file expired or was deleted
- `"Could not parse JSON response"`: Model returned non-JSON format

## Performance Considerations

- **Response Time**: QA queries typically take 3-10 seconds depending on complexity
- **File Size**: Optimal image size is 1-5MB for best performance
- **Concurrent Requests**: Mistral API supports concurrent requests but has rate limits
- **Caching**: Consider implementing response caching for repeated queries

## Future Enhancements

- [ ] Batch processing for multiple documents
- [ ] Custom prompt templates for specific document types
- [ ] Integration with vector databases for semantic search
- [ ] Multi-language document support
- [ ] Advanced table extraction and analysis
- [ ] Document comparison and change detection
