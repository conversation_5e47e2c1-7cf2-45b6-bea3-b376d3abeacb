#!/usr/bin/env python3
"""
Test script for Mistral OCR and QA integration.

This script tests the basic functionality of the Mistral integration
without requiring actual API calls (uses mock data for testing).
"""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all Mistral-related imports work correctly."""
    print("Testing imports...")
    
    try:
        from src.processors.ocr_processor import get_ocr_processor, MistralOCR
        print("✓ OCR processor imports successful")
    except ImportError as e:
        print(f"✗ OCR processor import failed: {e}")
        return False
    
    try:
        from src.agents.document_qa_agent import DocumentQAAgent
        print("✓ QA agent import successful")
    except ImportError as e:
        print(f"✗ QA agent import failed: {e}")
        return False
    
    try:
        from src.agents.processing_coordinator import ProcessingCoordinator
        print("✓ Processing coordinator import successful")
    except ImportError as e:
        print(f"✗ Processing coordinator import failed: {e}")
        return False
    
    return True

def test_mistral_availability():
    """Test if Mistral client is available."""
    print("\nTesting Mistral availability...")
    
    try:
        from mistralai import Mistral
        from mistralai.models.usermessage import UserMessage
        from mistralai.models.textchunk import TextChunk
        from mistralai.models.imageurlchunk import ImageURLChunk
        print("✓ Mistral client imports successful")
        return True
    except ImportError as e:
        print(f"✗ Mistral client not available: {e}")
        return False

def test_configuration():
    """Test configuration settings."""
    print("\nTesting configuration...")
    
    try:
        from src.config import settings
        
        # Check if Mistral settings are available
        if hasattr(settings, 'mistral_api_key'):
            print("✓ Mistral API key setting available")
        else:
            print("✗ Mistral API key setting not found")
            return False
            
        if hasattr(settings, 'mistral_model'):
            print(f"✓ Mistral model setting: {settings.mistral_model}")
        else:
            print("✗ Mistral model setting not found")
            return False
            
        return True
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_ocr_factory():
    """Test OCR processor factory with Mistral option."""
    print("\nTesting OCR factory...")
    
    try:
        from src.processors.ocr_processor import get_ocr_processor
        
        # Test that mistral is recognized as a valid provider
        try:
            # This will fail if API key is not set, but should not fail due to unknown provider
            ocr = get_ocr_processor("mistral")
            print("✓ Mistral OCR provider recognized")
            return True
        except ValueError as e:
            if "Unknown OCR provider" in str(e):
                print(f"✗ Mistral not recognized as OCR provider: {e}")
                return False
            else:
                # Expected error if API key not set
                print("✓ Mistral OCR provider recognized (API key not configured)")
                return True
        except Exception as e:
            print(f"✓ Mistral OCR provider recognized (initialization failed: {e})")
            return True
            
    except Exception as e:
        print(f"✗ OCR factory test failed: {e}")
        return False

def test_web_routes():
    """Test that QA routes are properly registered."""
    print("\nTesting web routes...")
    
    try:
        from src.web.routes import router
        
        # Check if QA routes are registered
        qa_routes = [route for route in router.routes if hasattr(route, 'path') and '/qa' in route.path]
        
        if qa_routes:
            print(f"✓ Found {len(qa_routes)} QA routes")
            for route in qa_routes:
                if hasattr(route, 'path') and hasattr(route, 'methods'):
                    print(f"  - {list(route.methods)[0] if route.methods else 'GET'} {route.path}")
            return True
        else:
            print("✗ No QA routes found")
            return False
            
    except Exception as e:
        print(f"✗ Web routes test failed: {e}")
        return False

def test_template_exists():
    """Test that QA template exists."""
    print("\nTesting QA template...")
    
    template_path = Path("src/web/templates/qa.html")
    if template_path.exists():
        print("✓ QA template exists")
        return True
    else:
        print("✗ QA template not found")
        return False

def main():
    """Run all tests."""
    print("Mistral Integration Test Suite")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_mistral_availability,
        test_configuration,
        test_ocr_factory,
        test_web_routes,
        test_template_exists
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Mistral integration is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please check the configuration.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
