#!/usr/bin/env python3
"""Debug actual lines from PDF to see what's happening"""

import sys
from pathlib import Path
import re

# Add src to path
sys.path.insert(0, str(Path.cwd()))

from src.processors.pdf_processor import PDFProcessor

def debug_actual_lines():
    """Debug actual lines from PDF."""
    
    # Load and extract text from PDF
    pdf_processor = PDFProcessor()
    document = pdf_processor.load_document(Path("example/onlyspares.pdf"))
    document = pdf_processor.extract_text(document)
    
    # Combine all page text
    full_text = ""
    for page_num, text in document.page_texts.items():
        full_text += f"\n{text}\n"
    
    lines = full_text.split('\n')
    
    print("Looking for lines that should be spare parts...")
    
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
        
        # Look for lines that start with a number followed by space and another number
        if re.match(r'^\d+[a-z]?\s+\d+\s+', line):
            print(f"Line {i}: '{line}'")
            parts = line.split()
            print(f"  Parts: {parts}")
            
            # Check if this would pass our filters
            pos_candidate = parts[0]
            qty_candidate = parts[1]
            
            print(f"  Position check: {re.match(r'^\d+[a-z]?$', pos_candidate) is not None}")
            print(f"  Quantity check: {qty_candidate.isdigit()}")
            
            # Check skip keywords
            skip_keywords = ['POS. NO', 'QTY.', 'SC PART NO', 'DESCRIPTION', 'NOTE!', 'FINAL', 
                            'SCANJET', 'Page', 'Telephone', 'Email', 'Web', 'Göteborg', 'Sweden']
            skip_this = any(keyword in line.upper() for keyword in skip_keywords)
            print(f"  Skip keywords check: {skip_this}")
            
            print()

if __name__ == "__main__":
    debug_actual_lines()
