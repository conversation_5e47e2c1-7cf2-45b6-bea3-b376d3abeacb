{"cells": [{"cell_type": "markdown", "metadata": {"id": "1Fzjyb9daweA"}, "source": ["# OCR Cookbook\n", "\n", "---\n", "\n", "## Enable Document Comprehension for Any Model with Tool Usage and OCR\n", "\n", "Optical Character Recognition (OCR) transforms text-based documents and images into pure text outputs and markdown. By leveraging this feature, you can enable any Large Language Model (LLM) to reliably understand documents efficiently and cost-effectively.\n", "\n", "In this guide, we will demonstrate how to use OCR with our models to discuss any text-based document, whether it's a PDF, photo, or screenshot, via URLs.\n", "\n", "---\n", "\n", "### Method\n", "We will leverage [Tool Usage](https://docs.mistral.ai/capabilities/function_calling/) to open any URL on demand by the user.\n", "\n", "#### Other Methods\n", "We also have a built-in feature for document understanding leveraging our OCR model, to learn more about it visit our [Document Understanding docs](https://docs.mistral.ai/capabilities/OCR/document_understanding/)"]}, {"cell_type": "markdown", "metadata": {"id": "KvEQoe7Y9-um"}, "source": ["## Tool Usage\n", "To achieve this, we will first send our question, which may or may not include URLs pointing to documents that we want to perform OCR on. Mistral Small will then decide, using the `open_urls` tool ( extracting the URLs directly ), whether it needs to perform OCR on any URL or if it can directly answer the question."]}, {"cell_type": "markdown", "metadata": {"id": "FL4ZJCeY918i"}, "source": ["![image.png](data:image/png;base64,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)"]}, {"cell_type": "markdown", "metadata": {"id": "Sf84okJJmm7M"}, "source": ["### Setup\n", "First, let's install `mist<PERSON><PERSON>`"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "X1EBW_a6gRUD", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "3d1c72f7-7eb5-44b0-c898-ce3d45d2dbcb"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting mist<PERSON><PERSON>\n", "  Downloading mistralai-1.5.0-py3-none-any.whl.metadata (29 kB)\n", "Collecting eval-type-backport>=0.2.0 (from mistralai)\n", "  Downloading eval_type_backport-0.2.2-py3-none-any.whl.metadata (2.2 kB)\n", "Requirement already satisfied: httpx>=0.27.0 in /usr/local/lib/python3.11/dist-packages (from mistralai) (0.28.1)\n", "Collecting jsonpath-python>=1.0.6 (from mistralai)\n", "  Downloading jsonpath_python-1.0.6-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: pydantic>=2.9.0 in /usr/local/lib/python3.11/dist-packages (from mistralai) (2.10.6)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from mistralai) (2.8.2)\n", "Collecting typing-inspect>=0.9.0 (from mistralai)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.11/dist-packages (from httpx>=0.27.0->mistralai) (3.7.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx>=0.27.0->mistralai) (2025.1.31)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx>=0.27.0->mistralai) (1.0.7)\n", "Requirement already satisfied: idna in /usr/local/lib/python3.11/dist-packages (from httpx>=0.27.0->mistralai) (3.10)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx>=0.27.0->mist<PERSON>ai) (0.14.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.9.0->mist<PERSON>ai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.9.0->mist<PERSON>ai) (2.27.2)\n", "Requirement already satisfied: typing-extensions>=4.12.2 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.9.0->mist<PERSON><PERSON>) (4.12.2)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->mist<PERSON><PERSON>) (1.17.0)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect>=0.9.0->mist<PERSON><PERSON>)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl.metadata (1.1 kB)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio->httpx>=0.27.0->mist<PERSON>ai) (1.3.1)\n", "Downloading mistralai-1.5.0-py3-none-any.whl (271 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m271.6/271.6 kB\u001b[0m \u001b[31m14.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading eval_type_backport-0.2.2-py3-none-any.whl (5.8 kB)\n", "Downloading jsonpath_python-1.0.6-py3-none-any.whl (7.6 kB)\n", "Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: mypy-extensions, jsonpath-python, eval-type-backport, typing-inspect, mistralai\n", "Successfully installed eval-type-backport-0.2.2 jsonpath-python-1.0.6 mistralai-1.5.0 mypy-extensions-1.0.0 typing-inspect-0.9.0\n"]}], "source": ["!pip install mistralai"]}, {"cell_type": "markdown", "metadata": {"id": "nTpiGWkpmvSb"}, "source": ["We can now set up our client. You can create an API key on our [Plateforme](https://console.mistral.ai/api-keys/)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "AwG2kwfTlbW1"}, "outputs": [], "source": ["from mistralai import Mistral\n", "\n", "api_key = \"API_KEY\"\n", "client = Mistral(api_key=api_key)\n", "text_model = \"mistral-small-latest\"\n", "ocr_model = \"mistral-ocr-latest\""]}, {"cell_type": "markdown", "metadata": {"id": "F35KDRN-nEMv"}, "source": ["### System and Tool\n", "For the model to be aware of its purpose and what it can do, it's important to provide a clear system prompt with instructions and explanations of any tools it may have access to.\n", "\n", "Let's define a system prompt and the tools it will have access to, in this case, `open_urls`.\n", "\n", "*Note: `open_urls` can easily be customized with other resources and models ( for summarization, for example ) and many other features. In this demo, we are going for a simpler approach.*"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zzgxk6qTgGU9"}, "outputs": [], "source": ["system = \"\"\"You are an AI Assistant with document understanding via URLs. You will be provided with URLs, and you must answer any questions related to those documents.\n", "\n", "# O<PERSON><PERSON> URLS INSTRUCTIONS\n", "You can open URLs by using the `open_urls` tool. It will open webpages and apply OCR to them, retrieving the contents. Use those contents to answer the user.\n", "Only URLs pointing to PDFs and images are supported; you may encounter an error if they are not; provide that information to the user if required.\"\"\""]}, {"cell_type": "code", "source": ["def _perform_ocr(url: str) -> str:\n", "    try:   # Apply OCR to the PDF URL\n", "        response = client.ocr.process(\n", "            model=ocr_model,\n", "            document={\n", "                \"type\": \"document_url\",\n", "                \"document_url\": url\n", "                }\n", "            )\n", "    except Exception:\n", "        try:  # IF PDF OCR fails, try Image OCR\n", "            response = client.ocr.process(\n", "                model=ocr_model,\n", "                document={\n", "                    \"type\": \"image_url\",\n", "                    \"image_url\": url\n", "                    }\n", "                )\n", "        except Exception as e:\n", "            return e  # Return the error to the model if it fails, otherwise return the contents\n", "    return \"\\n\\n\".join([f\"### Page {i+1}\\n{response.pages[i].markdown}\" for i in range(len(response.pages))])"], "metadata": {"id": "SxP7DlEHWXnK"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def open_urls(urls: list) -> str:\n", "    contents = \"# Documents\"\n", "    for url in urls:\n", "        contents += f\"\\n\\n## URL: {url}\\n{_perform_ocr(url)}\"\n", "    return contents"], "metadata": {"id": "s9PgX9fqWY1m"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "BagY4xg0nSSg"}, "source": ["We also have to define the Tool Schema that will be provided to our API and model.\n", "\n", "By following the [documentation](https://docs.mistral.ai/capabilities/function_calling/), we can create something like this:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hpBKzNOfliQr"}, "outputs": [], "source": ["tools = [\n", "    {\n", "        \"type\": \"function\",\n", "        \"function\": {\n", "            \"name\": \"open_urls\",\n", "            \"description\": \"Open URLs websites (PDFs and Images) and perform OCR on them.\",\n", "            \"parameters\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"urls\": {\n", "                        \"type\": \"array\",\n", "                        \"description\": \"The URLs list.\",\n", "                    }\n", "                },\n", "                \"required\": [\"urls\"],\n", "            },\n", "        },\n", "    },\n", "]"]}, {"cell_type": "code", "source": ["names_to_functions = {\n", "    'open_urls': open_urls\n", "}"], "metadata": {"id": "DqalxqIWWVL1"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "W6bE08lPngrm"}, "source": ["### Test\n", "Everything is ready; we can quickly create a while loop to chat with our model directly in the console.\n", "\n", "The model will use `open_urls` each time URLs are mentioned. If they are PDFs or photos, it will perform OCR and provide the raw text contents to the model, which will then use them to answer the user.\n", "\n", "#### Example Prompts ( PDF & Image )\n", "- Could you summarize what this research paper talks about? https://arxiv.org/pdf/2410.07073\n", "- What is written here: https://jeroen.github.io/images/testocr.png"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 371}, "id": "pVeVmWn_ljRo", "outputId": "7fe77386-cb5d-43f4-8bae-ea7b41ddb01a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Assistant > The research paper titled \"Pixtral 12B\" introduces a 12-billion-parameter multimodal language model designed to understand both natural images and documents. The model is trained on a large-scale dataset of interleaved image and text documents, enabling it to perform multi-turn, multi-image conversations. Pixtral 12B is built on a transformer architecture and includes a new vision encoder, PixtralViT, which allows it to process images at their native resolution and aspect ratio. This flexibility is achieved through a novel RoPE-2D implementation, which supports variable image sizes and aspect ratios without the need for interpolation.\n", "\n", "The model's performance is evaluated on various multimodal benchmarks, where it outperforms other open-source models of similar sizes, such as Qwen-2-VL 7B and Llama-3.2 11B. Pixtral 12B also matches or exceeds the performance of much larger models like Llama-3.2 90B and closed-source models like Claude-3 Haiku and Gemini-1.5 Flash 8B. The paper introduces a new benchmark, MM-MT-Bench, designed to evaluate multimodal models in practical scenarios, and provides detailed analysis and code for standardized evaluation protocols.\n", "\n", "The architecture of Pixtral 12B consists of a multimodal decoder and a vision encoder. The vision encoder, PixtralViT, is trained from scratch and includes several key features such as break tokens, gating in the feedforward layer, sequence packing, and RoPE-2D for relative position encoding. The model is evaluated under various prompts and metrics, demonstrating its robustness and flexibility in handling different types of multimodal tasks.\n", "\n", "The paper also discusses the importance of standardized evaluation protocols and the impact of prompt design on model performance. It highlights that Pixtral 12B performs well under both 'Explicit' and 'Naive' prompts, with only minor regressions on specific benchmarks. The model's performance is further analyzed under flexible parsing constraints, showing that it benefits very little from relaxed metrics and continues to lead even when flexible parsing is accounted for.\n", "\n", "In summary, Pixtral 12B is a state-of-the-art multimodal model that excels in both text-only and multimodal tasks. Its novel architecture, flexibility in processing images, and strong performance across various benchmarks make it a versatile tool for complex multimodal applications. The model is released under the Apache 2.0 license, making it accessible for further research and development.\n", "Assistant > The text written on the image is:\n", "\n", "\"This is a lot of 12 point text to test the ocr code and see if it works on all types of file format. The quick brown dog jumped over the lazy fox. The quick brown dog jumped over the lazy fox. The quick brown dog jumped over the lazy fox. The quick brown dog jumped over the lazy fox.\"\n", "Assistant > You're welcome! If you have any more questions or need further assistance, feel free to ask.\n"]}], "source": ["import json\n", "\n", "messages = [{\"role\": \"system\", \"content\": system}]\n", "while True:\n", "    # Insert user input, quit if desired\n", "    user_input = input(\"User > \")\n", "    if user_input == \"quit\":\n", "        break\n", "    messages.append({\"role\": \"user\", \"content\": user_input})\n", "\n", "    # Loop Mistral Small tool use until no tool called\n", "    while True:\n", "        response = client.chat.complete(\n", "            model = text_model,\n", "            messages = messages,\n", "            temperature = 0,\n", "            tools = tools\n", "        )\n", "        messages.append({\"role\":\"assistant\", \"content\": response.choices[0].message.content, \"tool_calls\": response.choices[0].message.tool_calls})\n", "\n", "        # If tool called, run tool and continue, else break loop and reply\n", "        if response.choices[0].message.tool_calls:\n", "            tool_call = response.choices[0].message.tool_calls[0]\n", "            function_name = tool_call.function.name\n", "            function_params = json.loads(tool_call.function.arguments)\n", "            function_result = names_to_functions[function_name](**function_params)\n", "            messages.append({\"role\":\"tool\", \"name\":function_name, \"content\":function_result, \"tool_call_id\":tool_call.id})\n", "        else:\n", "            break\n", "\n", "    print(\"Assistant >\", response.choices[0].message.content)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 0}