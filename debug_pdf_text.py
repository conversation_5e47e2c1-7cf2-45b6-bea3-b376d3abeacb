#!/usr/bin/env python3
"""Debug the PDF text extraction to see the actual structure"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path.cwd()))

from src.processors.pdf_processor import PDFProcessor

def debug_pdf_text():
    """Debug the PDF text extraction."""
    
    # Load and extract text from PDF
    pdf_processor = PDFProcessor()
    document = pdf_processor.load_document(Path("example/onlyspares.pdf"))
    document = pdf_processor.extract_text(document)
    
    print("=== PAGE TEXTS ===")
    for page_num, text in document.page_texts.items():
        print(f"\n--- PAGE {page_num} ---")
        lines = text.split('\n')
        for i, line in enumerate(lines[:30]):  # Show first 30 lines
            print(f"{i+1:2d}: '{line}'")
        if len(lines) > 30:
            print(f"... and {len(lines) - 30} more lines")
    
    # Show lines that look like spare parts data
    print("\n=== POTENTIAL SPARE PARTS LINES ===")
    full_text = ""
    for page_num, text in document.page_texts.items():
        full_text += f"\n{text}\n"
    
    lines = full_text.split('\n')
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
        
        # Look for lines that start with a number
        parts = line.split()
        if len(parts) >= 3:
            try:
                # Check if first part looks like position number
                pos_candidate = parts[0]
                if pos_candidate.isdigit() or (len(pos_candidate) <= 3 and pos_candidate[:-1].isdigit()):
                    # Check if second part looks like quantity
                    qty_candidate = parts[1]
                    if qty_candidate.startswith('(') and qty_candidate.endswith(')'):
                        qty_candidate = qty_candidate[1:-1]
                    
                    if qty_candidate.isdigit():
                        print(f"Line {i}: '{line}'")
                        print(f"  Parts: {parts}")
                        print()
            except:
                pass

if __name__ == "__main__":
    debug_pdf_text()
