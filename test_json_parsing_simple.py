#!/usr/bin/env python3
"""Simple test of the improved JSON parsing logic."""

import json
import re

def clean_json_string(json_str: str) -> str:
    """Clean up common JSON formatting issues from LLM responses."""
    # Remove trailing commas before closing braces/brackets
    json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
    
    # Replace null with None for Python compatibility, then back to null
    json_str = json_str.replace('null', 'None')
    json_str = json_str.replace('None', 'null')
    
    # Fix common quote issues
    json_str = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', json_str)
    
    return json_str.strip()

def parse_json_response(response_text: str):
    """Parse JSON response from LLM, with fallback handling."""
    try:
        # First, try to extract <PERSON><PERSON><PERSON> from markdown code blocks
        markdown_match = re.search(r'```(?:json)?\s*(\[.*?\])\s*```', response_text, re.DOTALL)
        if markdown_match:
            json_str = markdown_match.group(1)
        else:
            # Try to extract JSON array from response
            json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
            else:
                # Try parsing the entire response
                json_str = response_text
        
        # Clean up common JSON issues from LLM responses
        json_str = clean_json_string(json_str)
        
        return json.loads(json_str)
            
    except json.JSONDecodeError as e:
        print(f"JSON parsing failed: {e}")
        return None

def test_cases():
    """Test the improved parsing with problematic cases."""
    
    test_cases = [
        # Case 1: Trailing comma issue
        '''[
            {
                "name": "SPARE PART KIT",
                "part_number": "90T2-CRUDE",
                "subcomponent": "Maintenance",
                "material": null,
            }
        ]''',
        
        # Case 2: Markdown formatted
        '''```json
        [
            {
                "name": "Tank Cleaning Machine",
                "type": "Cleaning Equipment",
                "description": "Used for cleaning cargo tanks"
            }
        ]
        ```''',
        
        # Case 3: Multiple trailing commas
        '''[
            {
                "name": "High Pressure Pump",
                "type": "Pump",
                "description": "Provides high pressure water",
                "specifications": {
                    "pressure": "150 bar",
                    "flow_rate": "100 L/min",
                },
            },
        ]'''
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*50}")
        print(f"Test Case {i}")
        print(f"Input: {test_case[:80]}...")
        
        result = parse_json_response(test_case)
        if result:
            print(f"✅ Success! Parsed {len(result)} entities:")
            for entity in result:
                print(f"  - {entity.get('name', 'Unknown')}")
        else:
            print("❌ Failed to parse")

if __name__ == "__main__":
    test_cases()
