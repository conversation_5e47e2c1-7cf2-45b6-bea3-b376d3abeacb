version: '3.8'

services:
  pdf-understanding-tool:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - GOOGLE_APPLICATION_CREDENTIALS=${GOOGLE_APPLICATION_CREDENTIALS}
      - OCR_PROVIDER=${OCR_PROVIDER:-tesseract}
      - DEBUG=${DEBUG:-false}
      - HOST=0.0.0.0
      - PORT=8000
    volumes:
      - ./uploads:/app/uploads
      - ./outputs:/app/outputs
      - ./templates:/app/templates
      - ./logs:/app/logs
      # Mount Google credentials if using Google Vision
      - ${GOOGLE_APPLICATION_CREDENTIALS}:${GOOG<PERSON>_APPLICATION_CREDENTIALS}:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - pdf-understanding-tool
    restart: unless-stopped
    profiles:
      - production

volumes:
  uploads:
  outputs:
  templates:
  logs:
