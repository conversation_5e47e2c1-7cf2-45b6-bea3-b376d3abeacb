# PDF Understanding Tool for Shipping Industry

An AI-powered tool that automatically extracts machinery, subcomponents, and spare parts information from technical manuals and documentation in the shipping industry.

## Features

- **Smart Document Detection**: Automatically detects document type (text-based, scanned, or mixed)
- **Advanced OCR**: Supports Tesseract, AWS Textract, and Google Vision for scanned documents
- **AI-Powered Extraction**: Uses advanced NLP to extract structured information about:
  - Machinery (engines, pumps, compressors, etc.)
  - Subcomponents (bearings, seals, valves, etc.)
  - Spare parts (with part numbers, specifications, materials)
- **Excel Export**: Structured Excel output with customizable templates
- **Web Interface**: Easy-to-use web interface for upload and download
- **Batch Processing**: Process multiple documents at once
- **Template System**: Customizable Excel templates for different requirements

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd buildersystem
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and settings
   ```

4. **Install Tesseract (for OCR)**:
   - **Ubuntu/Debian**: `sudo apt-get install tesseract-ocr`
   - **macOS**: `brew install tesseract`
   - **Windows**: Download from [GitHub releases](https://github.com/UB-Mannheim/tesseract/wiki)

## Configuration

### Required API Keys

- **OpenAI API Key**: Required for AI-powered entity extraction
- **AWS Credentials**: Optional, for AWS Textract OCR
- **Google Cloud Credentials**: Optional, for Google Vision OCR

### Environment Variables

Create a `.env` file based on `.env.example`:

```env
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional OCR providers
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
GOOGLE_APPLICATION_CREDENTIALS=path/to/credentials.json

# OCR Settings
OCR_PROVIDER=tesseract  # tesseract, textract, or vision

# Application Settings
DEBUG=false
HOST=0.0.0.0
PORT=8000
```

## Usage

### Web Interface (Recommended)

1. **Start the web server**:
   ```bash
   python -m src.main web
   ```

2. **Open your browser** and go to `http://localhost:8000`

3. **Upload a PDF** and configure processing options

4. **Download the Excel result** when processing is complete

### Command Line Interface

#### Process a single file:
```bash
python -m src.main process manual.pdf
```

#### Process with custom template:
```bash
python -m src.main process manual.pdf --template templates/custom.xlsx
```

#### Batch process multiple files:
```bash
python -m src.main batch input_folder/ --output results/
```

#### Create default templates:
```bash
python -m src.main create-templates
```

### Python API

```python
from src.agents.processing_coordinator import ProcessingCoordinator
from src.exporters.excel_exporter import ExcelExporter
from pathlib import Path

# Initialize components
coordinator = ProcessingCoordinator()
exporter = ExcelExporter()

# Process document
result = coordinator.process_document(
    Path("manual.pdf"),
    use_ocr=True,
    extract_entities=True
)

# Export to Excel
output_path = exporter.export_processing_result(result)
print(f"Result saved to: {output_path}")
```

## Output Format

The tool generates Excel files with the following structure:

### Summary Sheet
- Document information
- Processing statistics
- Confidence scores

### Machinery Sheet
- Machinery Name
- Type (engine, pump, compressor, etc.)
- Manufacturer
- Model
- Technical Specifications
- Operating Conditions
- Page Reference

### Subcomponents Sheet
- Subcomponent Name
- Parent Machinery
- Function
- Material
- Dimensions
- Parameters
- Page Reference

### Spare Parts Sheet
- Part Name
- Part Number
- Parent Subcomponent
- Material
- Size/Dimensions
- Operating Pressure
- Manufacturer
- Supplier Information
- Page Reference

## Templates

The tool supports custom Excel templates for different industry requirements:

### Default Templates
- **Shipping Standard**: General shipping industry format
- **Machinery Focus**: Detailed machinery specifications
- **Spare Parts Catalog**: Comprehensive parts inventory

### Custom Templates
1. Create an Excel file with your desired structure
2. Upload via the web interface or place in `templates/` directory
3. Select the template when processing documents

## Architecture

The system uses a modular, agent-based architecture:

```
├── PDF Processor          # Extract text and images
├── OCR Processor          # Handle scanned documents
├── Document Analyzer      # Classify and structure content
├── Entity Extractor       # Extract machinery/parts info
├── Processing Coordinator # Orchestrate the pipeline
└── Excel Exporter         # Generate structured output
```

## Supported Document Types

- **Text-based PDFs**: Direct text extraction
- **Scanned PDFs**: OCR processing required
- **Mixed documents**: Combination of text and images
- **Technical manuals**: Engine, pump, machinery documentation
- **Parts catalogs**: Spare parts lists and specifications

## Performance

- **Processing Speed**: 1-5 minutes per document (depending on size and complexity)
- **File Size Limit**: 100MB (configurable)
- **Accuracy**: 85-95% for well-structured technical documents
- **OCR Quality**: Depends on image quality and OCR provider

## Troubleshooting

### Common Issues

1. **Tesseract not found**:
   - Install Tesseract OCR
   - Set `TESSERACT_CMD` in `.env` if needed

2. **Low extraction accuracy**:
   - Ensure document quality is good
   - Try different OCR providers
   - Check if document contains technical content

3. **Memory issues with large files**:
   - Reduce `CHUNK_SIZE` in settings
   - Process files individually instead of batch

4. **API rate limits**:
   - Reduce `MAX_TOKENS` setting
   - Add delays between requests
   - Use different LLM model

### Logs

Check application logs in `logs/app.log` for detailed error information.

## Development

### Project Structure
```
src/
├── agents/           # AI agents for processing
├── config.py         # Configuration settings
├── exporters/        # Excel export functionality
├── models/           # Data models
├── processors/       # PDF and OCR processing
├── web/             # Web interface
└── main.py          # CLI entry point
```

### Adding New Features

1. **New OCR Provider**: Implement `OCRProcessor` interface
2. **Custom Entity Types**: Extend entity models and extraction prompts
3. **Export Formats**: Create new exporter classes
4. **Templates**: Add template validation and mapping logic

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the logs for error details

## Acknowledgments

- Built with LangChain for AI orchestration
- Uses OpenAI GPT models for entity extraction
- Supports multiple OCR providers for flexibility
- Designed specifically for maritime industry needs
