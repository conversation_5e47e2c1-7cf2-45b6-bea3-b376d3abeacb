#!/usr/bin/env python3
"""Simple test to extract spare parts directly from known good lines"""

import sys
from pathlib import Path
import re

# Add src to path
sys.path.insert(0, str(Path.cwd()))

from src.models.entities import SparePart

def test_simple_extraction():
    """Test extraction with known good lines."""
    
    # Known good lines from the debug output
    test_lines = [
        "1 1 30064 Shaft",
        "2 1 30065 Washer", 
        "6 3 30063-1 Knob 1,5º",
        "43a 1 120319 New type replaces one of 43",
        "133 1 20013-44 Turbine T1 (Nozzle Ø25-Ø34)"
    ]
    
    spare_parts = []
    
    for line in test_lines:
        print(f"Processing: '{line}'")
        parts = line.split()
        print(f"  Split into: {parts}")
        
        if len(parts) >= 3:
            pos_candidate = parts[0]
            qty_candidate = parts[1]
            part_number = parts[2]
            description = ' '.join(parts[3:]) if len(parts) > 3 else "Spare Part"
            
            print(f"  Position: {pos_candidate}")
            print(f"  Quantity: {qty_candidate}")
            print(f"  Part Number: {part_number}")
            print(f"  Description: {description}")
            
            # Check position pattern
            if re.match(r'^\d+[a-z]?$', pos_candidate):
                print("  ✓ Position valid")
                
                # Check quantity
                if qty_candidate.isdigit():
                    print("  ✓ Quantity valid")
                    
                    quantity = int(qty_candidate)
                    
                    spare_part = SparePart(
                        name=description,
                        part_number=part_number,
                        position_number=pos_candidate,
                        quantity=quantity,
                        units="EA",
                        drawing_number="# SC 90T2-CRUDE-07",
                        spare_part_title="Spare parts breakdown SCANJET SC 90 T2 List dated 2008-07-10",
                        parent_machinery="Tank Cleaning Machine",
                        confidence_score=0.95
                    )
                    
                    spare_parts.append(spare_part)
                    print(f"  ✓ Created spare part: {spare_part.name}")
                else:
                    print(f"  ✗ Quantity invalid: '{qty_candidate}'")
            else:
                print(f"  ✗ Position invalid: '{pos_candidate}'")
        else:
            print(f"  ✗ Not enough parts: {len(parts)}")
        
        print()
    
    print(f"Total extracted: {len(spare_parts)}")
    for part in spare_parts:
        print(f"- {part.position_number}: {part.name} ({part.part_number}) Qty: {part.quantity}")

if __name__ == "__main__":
    test_simple_extraction()
