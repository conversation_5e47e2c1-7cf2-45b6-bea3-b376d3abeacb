# Troubleshooting Guide

This guide helps resolve common installation and runtime issues.

## Installation Issues

### Step-by-Step Installation

If the regular installation fails, try this approach:

1. **Use the installation script:**
   ```bash
   python install.py
   ```

2. **Or install manually in steps:**
   ```bash
   # Step 1: Upgrade pip
   python -m pip install --upgrade pip
   
   # Step 2: Install minimal requirements
   pip install -r requirements-minimal.txt
   
   # Step 3: Test basic functionality
   python test_installation.py
   
   # Step 4: Install optional packages (if needed)
   pip install -r requirements-optional.txt
   ```

### Common Installation Errors

#### 1. **Package Version Conflicts**
```bash
ERROR: pip's dependency resolver does not currently consider all the packages that are installed
```

**Solution:**
```bash
# Create fresh virtual environment
python -m venv fresh_env
source fresh_env/bin/activate  # On Windows: fresh_env\Scripts\activate
pip install --upgrade pip
pip install -r requirements-minimal.txt
```

#### 2. **PyMuPDF Installation Fails**
```bash
ERROR: Failed building wheel for PyMuPDF
```

**Solutions:**
```bash
# Option 1: Install pre-compiled wheel
pip install --upgrade pip wheel
pip install PyMuPDF

# Option 2: Use alternative name
pip install fitz

# Option 3: Install system dependencies (Linux)
sudo apt-get install python3-dev
```

#### 3. **OpenCV Installation Issues**
```bash
ERROR: Could not build wheels for opencv-python
```

**Solutions:**
```bash
# Option 1: Install headless version
pip install opencv-python-headless

# Option 2: Install system dependencies (Linux)
sudo apt-get install python3-opencv

# Option 3: Skip OpenCV (optional for basic functionality)
# Edit requirements.txt and comment out opencv-python
```

#### 4. **LangChain Version Conflicts**
```bash
ERROR: langchain-community requires langchain-core>=0.1.0
```

**Solution:**
```bash
# Install LangChain packages in specific order
pip install langchain-core
pip install langchain
pip install langchain-openai
pip install langchain-community
```

#### 5. **Tesseract Not Found**
```bash
TesseractNotFoundError: tesseract is not installed
```

**Solutions:**

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install tesseract-ocr tesseract-ocr-eng
```

**macOS:**
```bash
brew install tesseract
```

**Windows:**
1. Download from: https://github.com/UB-Mannheim/tesseract/wiki
2. Install and add to PATH
3. Or set TESSERACT_CMD in .env file

#### 6. **Memory Issues During Installation**
```bash
ERROR: Could not install packages due to an EnvironmentError: [Errno 28] No space left on device
```

**Solutions:**
```bash
# Clear pip cache
pip cache purge

# Install without cache
pip install --no-cache-dir -r requirements-minimal.txt

# Install packages one by one
pip install pdfplumber
pip install PyMuPDF
# ... continue with each package
```

## Runtime Issues

### 1. **Import Errors**
```python
ModuleNotFoundError: No module named 'src'
```

**Solution:**
```bash
# Make sure you're in the project root directory
cd /path/to/buildersystem

# Run with proper module path
python -m src.main web
# or
python run.py
```

### 2. **OpenAI API Errors**
```bash
openai.error.AuthenticationError: Incorrect API key provided
```

**Solution:**
1. Check your .env file has the correct API key:
   ```
   OPENAI_API_KEY=sk-your-actual-api-key-here
   ```
2. Verify the API key is valid at https://platform.openai.com/api-keys

### 3. **OCR Not Working**
```bash
TesseractNotFoundError: tesseract is not installed or it's not in your PATH
```

**Solutions:**
1. Install Tesseract (see above)
2. Set path in .env file:
   ```
   TESSERACT_CMD=/usr/bin/tesseract
   ```
3. Use alternative OCR provider:
   ```
   OCR_PROVIDER=textract  # or vision
   ```

### 4. **Web Interface Not Loading**
```bash
uvicorn.error: [Errno 48] Address already in use
```

**Solution:**
```bash
# Use different port
python run.py web --port 8001

# Or kill process using port 8000
lsof -ti:8000 | xargs kill -9  # macOS/Linux
netstat -ano | findstr :8000   # Windows
```

### 5. **File Upload Errors**
```bash
413 Request Entity Too Large
```

**Solution:**
1. Check file size (default limit is 100MB)
2. Increase limit in .env:
   ```
   MAX_FILE_SIZE_MB=200
   ```

### 6. **Processing Fails**
```bash
Error processing document: 'NoneType' object has no attribute 'content'
```

**Possible causes and solutions:**
1. **Empty or corrupted PDF**: Try with a different PDF file
2. **OCR failure**: Disable OCR temporarily to test
3. **API rate limits**: Wait a few minutes and try again
4. **Insufficient API credits**: Check your OpenAI account

## System-Specific Issues

### Windows

1. **Long path issues:**
   ```bash
   # Enable long paths in Windows
   git config --system core.longpaths true
   ```

2. **Permission errors:**
   ```bash
   # Run command prompt as administrator
   # Or use PowerShell with execution policy
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

### macOS

1. **Xcode command line tools:**
   ```bash
   xcode-select --install
   ```

2. **Homebrew dependencies:**
   ```bash
   brew install tesseract poppler
   ```

### Linux

1. **System dependencies:**
   ```bash
   sudo apt-get update
   sudo apt-get install -y \
       tesseract-ocr \
       tesseract-ocr-eng \
       poppler-utils \
       python3-dev \
       build-essential
   ```

## Performance Issues

### 1. **Slow Processing**
- **Reduce chunk size** in .env: `CHUNK_SIZE=500`
- **Disable OCR** for text-based PDFs
- **Use faster LLM model**: `LLM_MODEL=gpt-3.5-turbo`

### 2. **Memory Issues**
- **Process smaller files** (split large PDFs)
- **Reduce batch size**
- **Increase system memory** or use cloud instance

### 3. **API Rate Limits**
- **Reduce temperature**: `TEMPERATURE=0.1`
- **Reduce max tokens**: `MAX_TOKENS=1000`
- **Add delays** between requests

## Getting Help

If you're still having issues:

1. **Run the test script:**
   ```bash
   python test_installation.py
   ```

2. **Check logs:**
   ```bash
   # Look in logs/app.log for detailed error messages
   tail -f logs/app.log
   ```

3. **Enable debug mode:**
   ```bash
   # In .env file
   DEBUG=true
   
   # Or run with debug
   python run.py web --debug
   ```

4. **Minimal test:**
   ```python
   # Test basic imports
   python -c "import pdfplumber, fitz, pandas, fastapi; print('Basic imports work')"
   ```

5. **Create an issue** with:
   - Your operating system
   - Python version
   - Full error message
   - Steps to reproduce

## Alternative Installation Methods

### Using Conda
```bash
conda create -n pdf-tool python=3.11
conda activate pdf-tool
conda install -c conda-forge pandas openpyxl pillow
pip install -r requirements-minimal.txt
```

### Using Docker
```bash
# Build and run with Docker
docker build -t pdf-understanding-tool .
docker run -p 8000:8000 -e OPENAI_API_KEY=your-key pdf-understanding-tool
```

### Minimal Installation (Core Features Only)
```bash
# Install only essential packages
pip install pdfplumber PyMuPDF pandas openpyxl fastapi uvicorn python-dotenv openai langchain
```
