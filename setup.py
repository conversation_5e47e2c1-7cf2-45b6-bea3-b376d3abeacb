"""Setup script for the PDF Understanding Tool."""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    requirements = requirements_file.read_text().strip().split('\n')
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]

setup(
    name="pdf-understanding-tool",
    version="1.0.0",
    author="PDF Understanding Tool Team",
    author_email="<EMAIL>",
    description="AI-powered PDF understanding tool for the shipping industry",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/example/pdf-understanding-tool",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Manufacturing",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "mypy>=0.950",
        ],
        "aws": [
            "boto3>=1.34.0",
            "botocore>=1.34.0",
        ],
        "google": [
            "google-cloud-vision>=3.4.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "pdf-understanding-tool=src.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "src": [
            "web/templates/*.html",
            "web/static/*",
        ],
    },
    zip_safe=False,
    keywords="pdf, ai, nlp, shipping, maritime, ocr, entity-extraction",
    project_urls={
        "Bug Reports": "https://github.com/example/pdf-understanding-tool/issues",
        "Source": "https://github.com/example/pdf-understanding-tool",
        "Documentation": "https://github.com/example/pdf-understanding-tool/wiki",
    },
)
