# API Keys
OPENAI_API_KEY=********************************************************************************************************************************************************************

# AWS Credentials (for Textract OCR)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# Google Cloud Credentials (for Vision OCR)
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/google-credentials.json

# OCR Settings
OCR_PROVIDER=tesseract  # Options: tesseract, textract, vision
TESSERACT_CMD=/usr/bin/tesseract  # Path to tesseract executable (if needed)

# Application Settings
DEBUG=false
HOST=0.0.0.0
PORT=8000

# LLM Settings
LLM_MODEL=gpt-3.5-turbo
TEMPERATURE=0.1
MAX_TOKENS=2000

# Processing Settings
MAX_FILE_SIZE_MB=100
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
