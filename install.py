#!/usr/bin/env python3
"""
Installation script for PDF Understanding Tool.
This script helps troubleshoot and install dependencies step by step.
"""

import sys
import subprocess
import os
from pathlib import Path

def run_command(command, description=""):
    """Run a command and return success status."""
    print(f"\n{'='*50}")
    print(f"Running: {description or command}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True
        )
        print("✓ SUCCESS")
        if result.stdout:
            print("Output:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print("✗ FAILED")
        print("Error:", e.stderr)
        if e.stdout:
            print("Output:", e.stdout)
        return False

def check_python_version():
    """Check if Python version is compatible."""
    print("Checking Python version...")
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("✗ Python 3.8 or higher is required!")
        return False
    
    print("✓ Python version is compatible")
    return True

def upgrade_pip():
    """Upgrade pip to latest version."""
    print("\nUpgrading pip...")
    return run_command(
        f"{sys.executable} -m pip install --upgrade pip",
        "Upgrading pip"
    )

def install_minimal_requirements():
    """Install minimal requirements first."""
    print("\nInstalling minimal requirements...")
    
    # Core packages that usually work
    core_packages = [
        "wheel",
        "setuptools",
        "python-dotenv",
        "click",
        "tqdm",
        "loguru",
        "pydantic",
        "pandas",
        "openpyxl",
        "Pillow",
        "fastapi",
        "uvicorn",
        "python-multipart",
        "jinja2"
    ]
    
    failed_packages = []
    
    for package in core_packages:
        success = run_command(
            f"{sys.executable} -m pip install {package}",
            f"Installing {package}"
        )
        if not success:
            failed_packages.append(package)
    
    return len(failed_packages) == 0, failed_packages

def install_pdf_packages():
    """Install PDF processing packages."""
    print("\nInstalling PDF processing packages...")
    
    pdf_packages = [
        "pdfplumber",
        "PyMuPDF",
        "pytesseract"
    ]
    
    failed_packages = []
    
    for package in pdf_packages:
        success = run_command(
            f"{sys.executable} -m pip install {package}",
            f"Installing {package}"
        )
        if not success:
            failed_packages.append(package)
    
    return len(failed_packages) == 0, failed_packages

def install_ai_packages():
    """Install AI/ML packages."""
    print("\nInstalling AI/ML packages...")
    
    ai_packages = [
        "openai",
        "langchain",
        "langchain-openai",
        "langchain-community"
    ]
    
    failed_packages = []
    
    for package in ai_packages:
        success = run_command(
            f"{sys.executable} -m pip install {package}",
            f"Installing {package}"
        )
        if not success:
            failed_packages.append(package)
    
    return len(failed_packages) == 0, failed_packages

def install_optional_packages():
    """Install optional packages."""
    print("\nInstalling optional packages...")
    print("(These may fail on some systems - that's okay)")
    
    optional_packages = [
        "sentence-transformers",
        "transformers",
        "faiss-cpu",
        "chromadb",
        "opencv-python",
        "pdf2image",
        "boto3",
        "google-cloud-vision",
        "unstructured",
        "xlsxwriter"
    ]
    
    failed_packages = []
    
    for package in optional_packages:
        success = run_command(
            f"{sys.executable} -m pip install {package}",
            f"Installing {package} (optional)"
        )
        if not success:
            failed_packages.append(package)
    
    return failed_packages

def test_imports():
    """Test if critical imports work."""
    print("\nTesting critical imports...")
    
    critical_imports = [
        ("pdfplumber", "PDF processing"),
        ("fitz", "PyMuPDF"),
        ("pandas", "Data processing"),
        ("openpyxl", "Excel export"),
        ("fastapi", "Web framework"),
        ("openai", "AI processing"),
        ("langchain", "LangChain framework")
    ]
    
    failed_imports = []
    
    for module, description in critical_imports:
        try:
            __import__(module)
            print(f"✓ {module} ({description})")
        except ImportError as e:
            print(f"✗ {module} ({description}) - {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0, failed_imports

def create_env_file():
    """Create .env file from example."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("\nCreating .env file...")
        try:
            env_file.write_text(env_example.read_text())
            print("✓ .env file created from .env.example")
            print("⚠ Remember to add your API keys to .env file!")
            return True
        except Exception as e:
            print(f"✗ Failed to create .env file: {e}")
            return False
    elif env_file.exists():
        print("✓ .env file already exists")
        return True
    else:
        print("⚠ No .env.example file found")
        return False

def main():
    """Main installation process."""
    print("PDF Understanding Tool - Installation Script")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Upgrade pip
    if not upgrade_pip():
        print("⚠ Failed to upgrade pip, continuing anyway...")
    
    # Install packages step by step
    print("\n" + "="*60)
    print("STEP 1: Installing core packages")
    print("="*60)
    
    success, failed = install_minimal_requirements()
    if not success:
        print(f"✗ Failed to install core packages: {failed}")
        print("Try installing them manually:")
        for pkg in failed:
            print(f"  pip install {pkg}")
        return False
    
    print("\n" + "="*60)
    print("STEP 2: Installing PDF processing packages")
    print("="*60)
    
    success, failed = install_pdf_packages()
    if not success:
        print(f"✗ Failed to install PDF packages: {failed}")
        print("You may need to install system dependencies first.")
        return False
    
    print("\n" + "="*60)
    print("STEP 3: Installing AI/ML packages")
    print("="*60)
    
    success, failed = install_ai_packages()
    if not success:
        print(f"✗ Failed to install AI packages: {failed}")
        print("These are required for entity extraction.")
        return False
    
    print("\n" + "="*60)
    print("STEP 4: Installing optional packages")
    print("="*60)
    
    failed = install_optional_packages()
    if failed:
        print(f"⚠ Some optional packages failed to install: {failed}")
        print("This is okay - the system will work without them.")
    
    # Test imports
    print("\n" + "="*60)
    print("STEP 5: Testing imports")
    print("="*60)
    
    success, failed = test_imports()
    if not success:
        print(f"✗ Critical imports failed: {failed}")
        print("Installation incomplete.")
        return False
    
    # Create .env file
    create_env_file()
    
    # Final summary
    print("\n" + "="*60)
    print("INSTALLATION COMPLETE!")
    print("="*60)
    
    print("✓ All critical packages installed successfully")
    print("✓ System should be ready to use")
    
    print("\nNext steps:")
    print("1. Edit .env file and add your OpenAI API key")
    print("2. Run: python test_installation.py")
    print("3. Run: python run.py web")
    print("4. Open http://localhost:8000 in your browser")
    
    if failed:
        print(f"\nNote: Some optional packages failed: {failed}")
        print("You can install them later if needed.")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nInstallation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n\nUnexpected error: {e}")
        sys.exit(1)
