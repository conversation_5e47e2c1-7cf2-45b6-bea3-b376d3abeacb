#!/usr/bin/env python3
"""Debug the chunking to see why structured extraction isn't working"""

import sys
from pathlib import Path
import re

# Add src to path
sys.path.insert(0, str(Path.cwd()))

from src.processors.pdf_processor import PDFProcessor
from src.agents.document_analyzer import DocumentAnalyzerAgent

def debug_chunks():
    """Debug the chunking process."""
    
    # Load and extract text from PDF
    pdf_processor = PDFProcessor()
    document = pdf_processor.load_document(Path("example/onlyspares.pdf"))
    document = pdf_processor.extract_text(document)
    
    # Create chunks
    chunks = pdf_processor.chunk_document(document)
    print(f"Created {len(chunks)} chunks")
    
    # Create document sections
    document_analyzer = DocumentAnalyzerAgent()
    sections = document_analyzer.create_document_sections(document, chunks)
    
    print(f"Created {len(sections)} sections")
    
    # Check each section for tabular data
    for i, section in enumerate(sections):
        print(f"\n--- Section {i+1} (Pages {section.page_start}-{section.page_end}) ---")
        print(f"Content length: {len(section.content)} chars")
        
        # Look for tabular patterns
        lines = section.content.split('\n')
        tabular_lines = []
        
        for line in lines:
            line = line.strip()
            if re.match(r'^\d+[a-z]?\s+\d+\s+', line):
                tabular_lines.append(line)
        
        print(f"Tabular lines found: {len(tabular_lines)}")
        if tabular_lines:
            print("Sample tabular lines:")
            for line in tabular_lines[:5]:
                print(f"  '{line}'")
        
        # Show first few lines of content
        print("First 10 lines of content:")
        for j, line in enumerate(lines[:10]):
            print(f"  {j+1}: '{line}'")

if __name__ == "__main__":
    debug_chunks()
