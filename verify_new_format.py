#!/usr/bin/env python3
"""Verify the new Excel format matches the manual format."""

import pandas as pd
from pathlib import Path

def verify_new_format():
    """Verify the generated Excel file has the correct format."""
    
    # Find the test output file
    outputs_dir = Path("outputs")
    test_files = list(outputs_dir.glob("*Tank Cleaning Machine Manual*"))
    
    if not test_files:
        print("❌ No test output file found")
        return
    
    test_file = test_files[0]
    manual_file = Path("Tank Cleaning Machine.xlsx")
    
    print(f"🔍 Comparing formats:")
    print(f"   Test file: {test_file}")
    print(f"   Manual file: {manual_file}")
    
    # Check test file
    print(f"\n📋 Test File Structure:")
    test_excel = pd.ExcelFile(test_file)
    print(f"   Sheets: {test_excel.sheet_names}")
    
    for sheet_name in test_excel.sheet_names:
        df = pd.read_excel(test_file, sheet_name=sheet_name)
        print(f"   {sheet_name}: {df.shape[0]} rows, {df.shape[1]} columns")
        print(f"      Columns: {list(df.columns)}")
    
    # Check manual file
    print(f"\n📋 Manual File Structure:")
    manual_excel = pd.ExcelFile(manual_file)
    print(f"   Sheets: {manual_excel.sheet_names}")
    
    for sheet_name in manual_excel.sheet_names:
        df = pd.read_excel(manual_file, sheet_name=sheet_name)
        print(f"   {sheet_name}: {df.shape[0]} rows, {df.shape[1]} columns")
        print(f"      Columns: {list(df.columns)}")
    
    # Compare sheet structures
    print(f"\n🔄 Format Comparison:")
    
    # Check if sheet names match
    test_sheets = set(test_excel.sheet_names)
    manual_sheets = set(manual_excel.sheet_names)
    
    if test_sheets == manual_sheets:
        print("   ✅ Sheet names match perfectly")
    else:
        print(f"   ⚠️  Sheet name differences:")
        print(f"      Test only: {test_sheets - manual_sheets}")
        print(f"      Manual only: {manual_sheets - test_sheets}")
    
    # Check column structures for matching sheets
    for sheet_name in test_sheets.intersection(manual_sheets):
        test_df = pd.read_excel(test_file, sheet_name=sheet_name)
        manual_df = pd.read_excel(manual_file, sheet_name=sheet_name)
        
        test_cols = set(test_df.columns)
        manual_cols = set(manual_df.columns)
        
        print(f"\n   📊 {sheet_name} Sheet:")
        if test_cols == manual_cols:
            print(f"      ✅ Columns match perfectly")
        else:
            print(f"      ⚠️  Column differences:")
            print(f"         Test only: {test_cols - manual_cols}")
            print(f"         Manual only: {manual_cols - test_cols}")
            print(f"         Common: {test_cols.intersection(manual_cols)}")
        
        # Show sample data
        if not test_df.empty:
            print(f"      📝 Sample test data:")
            for col in list(test_df.columns)[:3]:  # Show first 3 columns
                sample_val = test_df[col].iloc[0] if not test_df[col].empty else "N/A"
                print(f"         {col}: {sample_val}")

if __name__ == "__main__":
    verify_new_format()
