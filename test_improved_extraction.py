#!/usr/bin/env python3
"""Test the improved spare parts extraction with onlyspares.pdf"""

import sys
from pathlib import Path
import pandas as pd

# Add src to path
sys.path.insert(0, str(Path.cwd()))

from src.processors.pdf_processor import PDFProcessor
from src.agents.document_analyzer import DocumentAnalyzerAgent
from src.agents.entity_extractor import EntityExtractorAgent
from src.exporters.excel_exporter import ExcelExporter
from src.models.entities import ProcessingResult
from src.config import settings

def test_improved_extraction():
    """Test the improved spare parts extraction."""
    try:
        print("Testing improved spare parts extraction...")
        
        # Initialize components
        pdf_processor = PDFProcessor()
        document_analyzer = DocumentAnalyzerAgent()
        entity_extractor = EntityExtractorAgent()
        excel_exporter = ExcelExporter()
        
        # Load the test PDF
        pdf_path = Path("example/onlyspares.pdf")
        if not pdf_path.exists():
            print(f"Error: {pdf_path} not found")
            return False
            
        print(f"Loading PDF: {pdf_path}")
        document = pdf_processor.load_document(pdf_path)
        
        # Extract text
        print("Extracting text...")
        document = pdf_processor.extract_text(document)
        
        # Create document sections
        print("Creating document sections...")
        chunks = pdf_processor.chunk_document(document)
        sections = document_analyzer.create_document_sections(document, chunks)
        
        print(f"Created {len(sections)} sections")
        
        # Extract spare parts from all sections
        all_spare_parts = []
        for section in sections:
            print(f"Processing section {section.section_id} (pages {section.page_start}-{section.page_end})")
            spare_parts = entity_extractor.extract_spare_parts(section)
            all_spare_parts.extend(spare_parts)
            print(f"  Found {len(spare_parts)} spare parts")
        
        print(f"\nTotal spare parts extracted: {len(all_spare_parts)}")
        
        # Show first few spare parts
        print("\nFirst 10 spare parts:")
        for i, part in enumerate(all_spare_parts[:10]):
            print(f"{i+1}. {part.name} (Part: {part.part_number}, Pos: {part.position_number}, Qty: {part.quantity})")
        
        # Create processing result
        result = ProcessingResult(
            document_id="test-spares",
            document_name="onlyspares.pdf",
            total_pages=document.total_pages,
            sections_processed=len(sections),
            spare_parts=all_spare_parts
        )
        
        # Export to Excel
        print("\nExporting to Excel...")
        output_path = excel_exporter.export_processing_result(result)
        print(f"Excel file created: {output_path}")
        
        # Compare with expected output
        print("\nComparing with expected output...")
        expected_path = Path("example/expectedoutput.xlsx")
        if expected_path.exists():
            expected_df = pd.read_excel(expected_path, sheet_name='Spares')
            print(f"Expected spare parts count: {len(expected_df)}")
            print(f"Extracted spare parts count: {len(all_spare_parts)}")
            
            # Show comparison of first few entries
            print("\nComparison of first 5 entries:")
            print("Expected:")
            print(expected_df[['Part Name', 'Part Number', 'Position Number', 'Quantity (Qty)']].head())
            
            print("\nExtracted:")
            extracted_data = []
            for part in all_spare_parts[:5]:
                extracted_data.append({
                    'Part Name': part.name,
                    'Part Number': part.part_number,
                    'Position Number': part.position_number,
                    'Quantity (Qty)': part.quantity
                })
            extracted_df = pd.DataFrame(extracted_data)
            print(extracted_df)
        
        return True
        
    except Exception as e:
        print(f"Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_improved_extraction()
    if success:
        print("\n✓ Test completed successfully")
    else:
        print("\n✗ Test failed")
        sys.exit(1)
