"""Document analysis agent for semantic understanding and classification."""

import json
from typing import List, Dict, Any, Optional
from langchain.llms import OpenAI
from langchain.chat_models import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.schema import HumanMessage, SystemMessage
from loguru import logger

from ..models.document import PDFDocument
from ..models.entities import DocumentSection
from ..config import settings


class DocumentAnalyzerAgent:
    """Agent for analyzing document structure and content classification."""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.llm_model,
            temperature=settings.temperature,
            max_tokens=settings.max_tokens,
            openai_api_key=settings.openai_api_key
        )
        
        self.section_classifier_prompt = PromptTemplate(
            input_variables=["text", "context"],
            template="""
            You are an expert in marine engineering documentation. Analyze the following text section and classify its content type.

            Context: This is from a ship manual or technical documentation.

            Text to analyze:
            {text}

            Additional context: {context}

            Classify this section into one of these categories:
            1. MACHINERY_OVERVIEW - General machinery descriptions, specifications
            2. SUBCOMPONENT_DETAILS - Detailed component information, assemblies
            3. SPARE_PARTS_LIST - Parts lists, catalogs, maintenance items
            4. OPERATING_PROCEDURES - Operating instructions, procedures
            5. MAINTENANCE_PROCEDURES - Maintenance, repair instructions
            6. TECHNICAL_SPECIFICATIONS - Technical data, parameters, ratings
            7. DIAGRAMS_SCHEMATICS - References to diagrams, drawings
            8. SAFETY_INFORMATION - Safety procedures, warnings
            9. OTHER - General information not fitting above categories

            Also identify:
            - Key entities mentioned (machinery, components, parts)
            - Technical parameters present
            - Relevance score (0-1) for spare parts extraction

            Return your analysis in JSON format:
            {{
                "section_type": "category_name",
                "confidence": 0.95,
                "key_entities": ["entity1", "entity2"],
                "technical_parameters": ["param1", "param2"],
                "relevance_score": 0.8,
                "reasoning": "Brief explanation of classification"
            }}
            """
        )
        
        self.content_summarizer_prompt = PromptTemplate(
            input_variables=["text"],
            template="""
            Summarize the key technical content from this marine engineering text.
            Focus on machinery, components, and spare parts information.

            Text:
            {text}

            Provide a structured summary including:
            - Main machinery/equipment mentioned
            - Key components and subassemblies
            - Technical specifications
            - Part numbers or identifiers
            - Any maintenance or operational information

            Keep the summary concise but comprehensive.
            """
        )
    
    def analyze_document_structure(self, document: PDFDocument) -> Dict[str, Any]:
        """Analyze overall document structure and content distribution."""
        try:
            logger.info(f"Analyzing document structure for {document.filename}")
            
            # Analyze page distribution
            page_analysis = self._analyze_page_distribution(document)
            
            # Identify potential sections based on content patterns
            sections = self._identify_sections(document)
            
            # Classify content types across the document
            content_distribution = self._analyze_content_distribution(document)
            
            structure_analysis = {
                "document_id": document.document_id,
                "total_pages": document.total_pages,
                "page_analysis": page_analysis,
                "identified_sections": sections,
                "content_distribution": content_distribution,
                "processing_recommendations": self._generate_processing_recommendations(
                    page_analysis, content_distribution
                )
            }
            
            logger.info(f"Document structure analysis completed: {len(sections)} sections identified")
            return structure_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing document structure: {str(e)}")
            return {"error": str(e)}
    
    def classify_text_section(self, text: str, context: str = "") -> Dict[str, Any]:
        """Classify a text section and extract metadata."""
        try:
            # Prepare the prompt
            messages = [
                SystemMessage(content="You are an expert marine engineering document analyst."),
                HumanMessage(content=self.section_classifier_prompt.format(text=text, context=context))
            ]
            
            # Get classification
            response = self.llm(messages)
            
            # Parse JSON response
            try:
                classification = json.loads(response.content)
                return classification
            except json.JSONDecodeError:
                # Fallback parsing if JSON is malformed
                return self._parse_classification_fallback(response.content)
                
        except Exception as e:
            logger.error(f"Error classifying text section: {str(e)}")
            return {
                "section_type": "OTHER",
                "confidence": 0.0,
                "key_entities": [],
                "technical_parameters": [],
                "relevance_score": 0.0,
                "reasoning": f"Classification failed: {str(e)}"
            }
    
    def create_document_sections(self, document: PDFDocument, chunks: List[Dict]) -> List[DocumentSection]:
        """Create structured document sections from chunks."""
        sections = []
        
        for i, chunk in enumerate(chunks):
            try:
                # Classify the chunk
                classification = self.classify_text_section(
                    chunk["text"], 
                    f"Page {chunk['page_start']}-{chunk['page_end']}"
                )
                
                # Create section
                section = DocumentSection(
                    section_id=chunk["chunk_id"],
                    content=chunk["text"],
                    page_start=chunk["page_start"],
                    page_end=chunk["page_end"],
                    section_type=classification.get("section_type", "OTHER"),
                    entities_found=classification.get("key_entities", [])
                )
                
                # Add classification metadata
                section.metadata = {
                    "classification": classification,
                    "chunk_type": chunk.get("chunk_type", "unknown")
                }
                
                sections.append(section)
                
            except Exception as e:
                logger.warning(f"Error creating section from chunk {i}: {str(e)}")
                continue
        
        logger.info(f"Created {len(sections)} document sections")
        return sections
    
    def _analyze_page_distribution(self, document: PDFDocument) -> Dict[str, Any]:
        """Analyze how content is distributed across pages."""
        analysis = {
            "text_pages": len(document.text_extractable_pages),
            "scanned_pages": len(document.scanned_pages),
            "empty_pages": len(document.empty_pages),
            "text_density": {},
            "content_patterns": []
        }
        
        # Analyze text density per page
        for page_num, text in document.page_texts.items():
            word_count = len(text.split()) if text else 0
            char_count = len(text) if text else 0
            analysis["text_density"][page_num] = {
                "word_count": word_count,
                "char_count": char_count,
                "density_score": min(word_count / 100, 1.0)  # Normalized density
            }
        
        return analysis
    
    def _identify_sections(self, document: PDFDocument) -> List[Dict[str, Any]]:
        """Identify potential document sections based on content patterns."""
        sections = []
        
        # Simple heuristic-based section identification
        current_section = None
        
        for page_num in sorted(document.page_texts.keys()):
            text = document.page_texts[page_num]
            if not text:
                continue
            
            # Look for section headers (simple heuristic)
            lines = text.split('\n')
            for line in lines[:5]:  # Check first few lines of each page
                line = line.strip()
                if self._is_potential_header(line):
                    if current_section:
                        current_section["end_page"] = page_num - 1
                        sections.append(current_section)
                    
                    current_section = {
                        "title": line,
                        "start_page": page_num,
                        "end_page": page_num,
                        "type": self._infer_section_type(line)
                    }
                    break
        
        # Close last section
        if current_section:
            current_section["end_page"] = document.total_pages
            sections.append(current_section)
        
        return sections
    
    def _analyze_content_distribution(self, document: PDFDocument) -> Dict[str, Any]:
        """Analyze distribution of different content types."""
        distribution = {
            "machinery_content": 0,
            "parts_content": 0,
            "technical_specs": 0,
            "procedures": 0,
            "other": 0
        }
        
        total_text = len(document.raw_text) if document.raw_text else 1
        
        # Simple keyword-based analysis
        machinery_keywords = settings.machinery_keywords
        parts_keywords = settings.spare_part_keywords
        
        for page_text in document.page_texts.values():
            if not page_text:
                continue
                
            text_lower = page_text.lower()
            
            # Count keyword occurrences
            machinery_score = sum(1 for kw in machinery_keywords if kw in text_lower)
            parts_score = sum(1 for kw in parts_keywords if kw in text_lower)
            
            # Classify page content
            if machinery_score > parts_score:
                distribution["machinery_content"] += len(page_text)
            elif parts_score > 0:
                distribution["parts_content"] += len(page_text)
            else:
                distribution["other"] += len(page_text)
        
        # Normalize to percentages
        for key in distribution:
            distribution[key] = distribution[key] / total_text
        
        return distribution
    
    def _generate_processing_recommendations(self, page_analysis: Dict, content_distribution: Dict) -> List[str]:
        """Generate recommendations for processing strategy."""
        recommendations = []
        
        # OCR recommendations
        scanned_ratio = page_analysis["scanned_pages"] / (page_analysis["text_pages"] + page_analysis["scanned_pages"])
        if scanned_ratio > 0.5:
            recommendations.append("High OCR processing required - consider using premium OCR service")
        elif scanned_ratio > 0.2:
            recommendations.append("Moderate OCR processing needed")
        
        # Content processing recommendations
        if content_distribution["parts_content"] > 0.3:
            recommendations.append("Document contains significant spare parts information")
        
        if content_distribution["machinery_content"] > 0.4:
            recommendations.append("Focus on machinery extraction - rich technical content detected")
        
        return recommendations
    
    def _is_potential_header(self, line: str) -> bool:
        """Simple heuristic to identify potential section headers."""
        if len(line) < 5 or len(line) > 100:
            return False
        
        # Check for common header patterns
        header_indicators = [
            line.isupper(),
            line.endswith(':'),
            any(word in line.lower() for word in ['chapter', 'section', 'part', 'appendix']),
            line.replace(' ', '').replace('.', '').isdigit()  # Numbered sections
        ]
        
        return any(header_indicators)
    
    def _infer_section_type(self, header: str) -> str:
        """Infer section type from header text."""
        header_lower = header.lower()
        
        if any(word in header_lower for word in ['part', 'spare', 'component']):
            return "SPARE_PARTS"
        elif any(word in header_lower for word in ['engine', 'motor', 'pump', 'machinery']):
            return "MACHINERY"
        elif any(word in header_lower for word in ['maintenance', 'service', 'repair']):
            return "MAINTENANCE"
        elif any(word in header_lower for word in ['operation', 'operating', 'procedure']):
            return "OPERATION"
        else:
            return "GENERAL"
    
    def _parse_classification_fallback(self, response_text: str) -> Dict[str, Any]:
        """Fallback parser for malformed JSON responses."""
        return {
            "section_type": "OTHER",
            "confidence": 0.5,
            "key_entities": [],
            "technical_parameters": [],
            "relevance_score": 0.5,
            "reasoning": "Fallback classification due to parsing error"
        }
