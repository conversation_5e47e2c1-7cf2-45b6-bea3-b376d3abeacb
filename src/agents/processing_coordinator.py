"""Processing coordinator agent that orchestrates the entire document processing pipeline."""

import time
from typing import List, Dict, Any, Optional
from pathlib import Path
from loguru import logger

from ..models.document import PDFDocument, ProcessingStatus
from ..models.entities import ProcessingResult, DocumentSection
from ..processors.pdf_processor import PDFProcessor
from ..processors.ocr_processor import get_ocr_processor
from ..agents.document_analyzer import DocumentAnalyzerAgent
from ..agents.entity_extractor import EntityExtractorAgent
from ..config import settings


class ProcessingCoordinator:
    """Main coordinator for the document processing pipeline."""
    
    def __init__(self):
        self.pdf_processor = PDFProcessor()
        self.document_analyzer = DocumentAnalyzerAgent()
        self.entity_extractor = EntityExtractorAgent()
        self.ocr_processor = None
        
        # Initialize OCR processor if needed
        try:
            self.ocr_processor = get_ocr_processor(settings.ocr_provider)
            logger.info(f"OCR processor initialized: {settings.ocr_provider}")
        except Exception as e:
            logger.warning(f"OCR processor not available: {str(e)}")
    
    def process_document(self, file_path: Path, 
                        use_ocr: bool = True,
                        extract_entities: bool = True) -> ProcessingResult:
        """Process a complete PDF document through the entire pipeline."""
        start_time = time.time()
        
        try:
            logger.info(f"Starting document processing: {file_path.name}")
            
            # Step 1: Load and analyze PDF
            document = self.pdf_processor.load_document(file_path)
            document.update_status(ProcessingStatus.PROCESSING)
            
            # Step 2: Extract text from PDF
            document = self.pdf_processor.extract_text(document)
            
            # Step 3: OCR processing if needed
            if use_ocr and document.requires_ocr and self.ocr_processor:
                document = self._process_ocr(document)
            
            # Step 4: Document structure analysis
            structure_analysis = self.document_analyzer.analyze_document_structure(document)
            
            # Step 5: Create document chunks/sections
            chunks = self.pdf_processor.chunk_document(document)
            sections = self.document_analyzer.create_document_sections(document, chunks)
            
            # Step 6: Entity extraction
            processing_result = ProcessingResult(
                document_id=document.document_id,
                document_name=document.filename,
                total_pages=document.total_pages,
                sections_processed=len(sections)
            )
            
            if extract_entities:
                processing_result = self._extract_all_entities(sections, processing_result)
            
            # Step 7: Finalize processing
            processing_time = time.time() - start_time
            processing_result.processing_time_seconds = processing_time
            
            document.update_status(ProcessingStatus.COMPLETED)
            
            logger.info(f"Document processing completed in {processing_time:.2f} seconds")
            logger.info(f"Extracted: {len(processing_result.machinery)} machinery, "
                       f"{len(processing_result.subcomponents)} subcomponents, "
                       f"{len(processing_result.spare_parts)} spare parts, "
                       f"{len(processing_result.maintenance_jobs)} maintenance jobs")
            
            return processing_result
            
        except Exception as e:
            logger.error(f"Error processing document: {str(e)}")
            if 'document' in locals():
                document.update_status(ProcessingStatus.FAILED, str(e))
            
            # Return partial result
            return ProcessingResult(
                document_id=getattr(document, 'document_id', 'unknown'),
                document_name=file_path.name,
                total_pages=getattr(document, 'total_pages', 0),
                sections_processed=0,
                processing_time_seconds=time.time() - start_time
            )
    
    def _process_ocr(self, document: PDFDocument) -> PDFDocument:
        """Process OCR for scanned pages."""
        try:
            logger.info(f"Starting OCR processing for {len(document.scanned_pages)} pages")
            
            # Extract images for OCR
            image_paths = self.pdf_processor.extract_images_for_ocr(document)
            
            if not image_paths:
                logger.warning("No images extracted for OCR")
                return document
            
            total_confidence = 0
            processed_pages = 0
            
            for i, image_path in enumerate(image_paths):
                try:
                    page_num = document.scanned_pages[i] if i < len(document.scanned_pages) else i + 1
                    
                    # Perform OCR
                    text, confidence = self.ocr_processor.extract_text(image_path)
                    
                    if text and confidence > 0.3:  # Minimum confidence threshold
                        document.add_page_text(page_num, text)
                        total_confidence += confidence
                        processed_pages += 1
                        
                        logger.debug(f"OCR completed for page {page_num} (confidence: {confidence:.2f})")
                    
                    # Clean up temporary image
                    try:
                        image_path.unlink()
                    except:
                        pass
                        
                except Exception as e:
                    logger.warning(f"OCR failed for image {i}: {str(e)}")
                    continue
            
            # Update document OCR metadata
            if processed_pages > 0:
                document.ocr_provider = settings.ocr_provider
                document.ocr_confidence = total_confidence / processed_pages
                logger.info(f"OCR processing completed: {processed_pages} pages, "
                           f"avg confidence: {document.ocr_confidence:.2f}")
            
            return document
            
        except Exception as e:
            logger.error(f"Error in OCR processing: {str(e)}")
            return document
    
    def _extract_all_entities(self, sections: List[DocumentSection], 
                            result: ProcessingResult) -> ProcessingResult:
        """Extract entities from all document sections."""
        try:
            logger.info(f"Starting entity extraction from {len(sections)} sections")
            
            all_machinery = []
            all_subcomponents = []
            all_spare_parts = []
            all_maintenance_jobs = []

            # Process each section
            for section in sections:
                try:
                    entities = self.entity_extractor.extract_entities_from_section(section)

                    all_machinery.extend(entities["machinery"])
                    all_subcomponents.extend(entities["subcomponents"])
                    all_spare_parts.extend(entities["spare_parts"])
                    all_maintenance_jobs.extend(entities["maintenance_jobs"])
                    
                except Exception as e:
                    logger.warning(f"Error extracting entities from section {section.section_id}: {str(e)}")
                    continue
            
            # Establish relationships between entities
            self.entity_extractor.establish_relationships(
                all_machinery, all_subcomponents, all_spare_parts
            )
            
            # Update result
            result.machinery = all_machinery
            result.subcomponents = all_subcomponents
            result.spare_parts = all_spare_parts
            result.maintenance_jobs = all_maintenance_jobs

            # Calculate extraction statistics
            result.extraction_stats = {
                "machinery_count": len(all_machinery),
                "subcomponents_count": len(all_subcomponents),
                "spare_parts_count": len(all_spare_parts),
                "maintenance_jobs_count": len(all_maintenance_jobs),
                "total_entities": len(all_machinery) + len(all_subcomponents) + len(all_spare_parts) + len(all_maintenance_jobs)
            }
            
            # Calculate confidence scores
            confidence_scores = []
            for entity_list in [all_machinery, all_subcomponents, all_spare_parts]:
                for entity in entity_list:
                    if hasattr(entity, 'confidence_score') and entity.confidence_score:
                        confidence_scores.append(entity.confidence_score)
            
            if confidence_scores:
                result.confidence_scores = {
                    "average": sum(confidence_scores) / len(confidence_scores),
                    "min": min(confidence_scores),
                    "max": max(confidence_scores)
                }
            
            logger.info(f"Entity extraction completed: {result.extraction_stats}")
            return result
            
        except Exception as e:
            logger.error(f"Error in entity extraction: {str(e)}")
            return result
    
    def process_batch(self, file_paths: List[Path], 
                     use_ocr: bool = True,
                     extract_entities: bool = True) -> List[ProcessingResult]:
        """Process multiple documents in batch."""
        results = []
        
        logger.info(f"Starting batch processing of {len(file_paths)} documents")
        
        for i, file_path in enumerate(file_paths, 1):
            try:
                logger.info(f"Processing document {i}/{len(file_paths)}: {file_path.name}")
                
                result = self.process_document(file_path, use_ocr, extract_entities)
                results.append(result)
                
            except Exception as e:
                logger.error(f"Error processing {file_path.name}: {str(e)}")
                # Add error result
                results.append(ProcessingResult(
                    document_id=f"error_{i}",
                    document_name=file_path.name,
                    total_pages=0,
                    sections_processed=0
                ))
                continue
        
        logger.info(f"Batch processing completed: {len(results)} results")
        return results
    
    def get_processing_status(self, document_id: str) -> Dict[str, Any]:
        """Get processing status for a document (placeholder for future implementation)."""
        # This would typically query a database or cache
        return {
            "document_id": document_id,
            "status": "unknown",
            "message": "Status tracking not implemented"
        }
    
    def cancel_processing(self, document_id: str) -> bool:
        """Cancel processing for a document (placeholder for future implementation)."""
        logger.info(f"Processing cancellation requested for document {document_id}")
        # This would typically set a cancellation flag
        return True
