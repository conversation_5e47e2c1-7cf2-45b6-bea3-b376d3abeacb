"""Document QA Agent using Mistral OCR capabilities."""

import json
import base64
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from loguru import logger
from PIL import Image
import fitz  # PyMuPDF

from ..processors.ocr_processor import MistralOCR
from ..config import settings

try:
    from mistralai import Mistral
    from mistralai.models.usermessage import UserMessage
    from mistralai.models.textchunk import TextChunk
    from mistralai.models.imageurlchunk import ImageURLChunk
    MISTRAL_AVAILABLE = True
except ImportError:
    MISTRAL_AVAILABLE = False
    logger.warning("Mistral AI client not available")


class DocumentQAAgent:
    """Agent for document question answering using Mistral's OCR and understanding capabilities."""
    
    def __init__(self):
        """Initialize the QA agent."""
        if not MISTRAL_AVAILABLE:
            raise ImportError("Mistral AI client not available. Install with: pip install mistralai")
        
        try:
            self.client = Mistral(api_key=settings.mistral_api_key)
            self.model = settings.mistral_model
            self.ocr_processor = MistralOCR()
            logger.info("Document QA Agent initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Document QA Agent: {str(e)}")
            raise
    
    def _is_pdf(self, file_path: Path) -> bool:
        """Check if the file is a PDF."""
        return file_path.suffix.lower() == '.pdf'

    def _convert_pdf_to_image(self, pdf_path: Path, page_num: int = 0) -> Path:
        """Convert PDF page to image."""
        try:
            # Open PDF
            doc = fitz.open(pdf_path)

            if page_num >= len(doc):
                page_num = 0  # Default to first page

            # Get the page
            page = doc[page_num]

            # Convert to image (300 DPI for good quality)
            mat = fitz.Matrix(300/72, 300/72)  # 300 DPI
            pix = page.get_pixmap(matrix=mat)

            # Save as temporary image
            temp_image_path = settings.temp_dir / f"temp_pdf_page_{pdf_path.stem}_{page_num}.png"
            pix.save(str(temp_image_path))

            doc.close()
            logger.info(f"Converted PDF page {page_num} to image: {temp_image_path}")
            return temp_image_path

        except Exception as e:
            logger.error(f"Error converting PDF to image: {str(e)}")
            raise

    def _prepare_image_for_api(self, file_path: Path) -> tuple[Path, bool]:
        """Prepare image for Mistral API, converting PDF if necessary."""
        is_temp = False

        if self._is_pdf(file_path):
            # Convert PDF to image
            logger.info(f"Converting PDF to image: {file_path}")
            image_path = self._convert_pdf_to_image(file_path)
            is_temp = True
        else:
            # Use image directly, but validate it's a supported format
            supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
            if file_path.suffix.lower() not in supported_formats:
                raise ValueError(f"Unsupported image format: {file_path.suffix}")
            image_path = file_path

        return image_path, is_temp

    def _encode_image_to_base64(self, image_path: Path) -> str:
        """Encode image to base64 for Mistral API."""
        try:
            # Validate image can be opened
            with Image.open(image_path) as img:
                # Convert to RGB if necessary (for PNG with transparency, etc.)
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # Save as JPEG to ensure compatibility
                temp_jpeg_path = settings.temp_dir / f"temp_converted_{image_path.stem}.jpg"
                img.save(temp_jpeg_path, 'JPEG', quality=95)

                # Read and encode
                with open(temp_jpeg_path, 'rb') as image_file:
                    image_data = image_file.read()
                    encoded = base64.b64encode(image_data).decode('utf-8')

                # Clean up temporary JPEG
                try:
                    temp_jpeg_path.unlink()
                except:
                    pass

                return encoded

        except Exception as e:
            logger.error(f"Error encoding image to base64: {str(e)}")
            raise
    
    def answer_question(self, image_path: Path, question: str) -> Dict[str, Any]:
        """Answer a question about a document image."""
        temp_image_path = None
        try:
            # Prepare image (convert PDF if necessary)
            prepared_image_path, is_temp = self._prepare_image_for_api(image_path)
            if is_temp:
                temp_image_path = prepared_image_path

            # Encode image
            base64_image = self._encode_image_to_base64(prepared_image_path)
            
            # Prepare message for QA
            messages = [
                UserMessage(
                    content=[
                        TextChunk(
                            text=f"""Analyze this document image and answer the following question: {question}

Please provide:
1. A direct answer to the question
2. Supporting evidence from the document
3. Confidence level (high/medium/low)
4. Any relevant context or additional information

If the answer cannot be found in the document, please state that clearly."""
                        ),
                        ImageURLChunk(
                            image_url=f"data:image/jpeg;base64,{base64_image}"
                        )
                    ]
                )
            ]

            # Call Mistral API
            response = self.client.chat.complete(
                model=self.model,
                messages=messages,
                max_tokens=2000
            )
            
            answer = response.choices[0].message.content.strip()
            
            return {
                "question": question,
                "answer": answer,
                "image_path": str(image_path),
                "model": self.model,
                "success": True
            }

        except Exception as e:
            logger.error(f"Error answering question about {image_path}: {str(e)}")
            return {
                "question": question,
                "answer": f"Error processing question: {str(e)}",
                "image_path": str(image_path),
                "model": self.model,
                "success": False
            }
        finally:
            # Clean up temporary image if created
            if temp_image_path and temp_image_path.exists():
                try:
                    temp_image_path.unlink()
                except:
                    pass
    
    def extract_spare_parts_with_qa(self, image_path: Path) -> Dict[str, Any]:
        """Extract spare parts information using QA approach."""
        temp_image_path = None
        try:
            # Prepare image (convert PDF if necessary)
            prepared_image_path, is_temp = self._prepare_image_for_api(image_path)
            if is_temp:
                temp_image_path = prepared_image_path

            # Encode image
            base64_image = self._encode_image_to_base64(prepared_image_path)
            
            # Prepare message for spare parts extraction
            messages = [
                UserMessage(
                    content=[
                        TextChunk(
                            text="""Analyze this document image and extract spare parts information. This appears to be a spare parts breakdown or parts list.

Please extract the following information and return it as a JSON object:

{
    "document_info": {
        "equipment_name": "name of the equipment",
        "drawing_number": "drawing or document number",
        "spare_part_title": "title of the spare parts list",
        "date": "document date if available"
    },
    "spare_parts": [
        {
            "position_number": "position number (POS. NO)",
            "quantity": "quantity (QTY)",
            "part_number": "part number (SC PART NO or similar)",
            "name": "part description/name",
            "material": "material if specified",
            "remarks": "any additional notes"
        }
    ]
}

Look for structured tables with columns like:
- POS. NO (Position Number)
- QTY. (Quantity)
- SC PART NO. (Part Number)
- DESCRIPTION (Part Name/Description)

Extract all visible spare parts from the document."""
                        ),
                        ImageURLChunk(
                            image_url=f"data:image/jpeg;base64,{base64_image}"
                        )
                    ]
                )
            ]

            # Call Mistral API
            response = self.client.chat.complete(
                model=self.model,
                messages=messages,
                max_tokens=4000
            )
            
            try:
                # Try to parse JSON response
                result = json.loads(response.choices[0].message.content)
                result["success"] = True
                result["image_path"] = str(image_path)
                return result
                
            except json.JSONDecodeError:
                # Fallback to text response
                return {
                    "document_info": {},
                    "spare_parts": [],
                    "raw_response": response.choices[0].message.content,
                    "success": False,
                    "image_path": str(image_path),
                    "error": "Could not parse JSON response"
                }
            
        except Exception as e:
            logger.error(f"Error extracting spare parts from {image_path}: {str(e)}")
            return {
                "document_info": {},
                "spare_parts": [],
                "success": False,
                "image_path": str(image_path),
                "error": str(e)
            }
        finally:
            # Clean up temporary image if created
            if temp_image_path and temp_image_path.exists():
                try:
                    temp_image_path.unlink()
                except:
                    pass
    
    def extract_maintenance_jobs_with_qa(self, image_path: Path) -> Dict[str, Any]:
        """Extract maintenance job information using QA approach."""
        temp_image_path = None
        try:
            # Prepare image (convert PDF if necessary)
            prepared_image_path, is_temp = self._prepare_image_for_api(image_path)
            if is_temp:
                temp_image_path = prepared_image_path

            # Encode image
            base64_image = self._encode_image_to_base64(prepared_image_path)
            
            # Prepare message for maintenance jobs extraction
            messages = [
                UserMessage(
                    content=[
                        TextChunk(
                            text="""Analyze this document image and extract maintenance job information. Look for maintenance schedules, inspection intervals, or service requirements.

Please extract the following information and return it as a JSON object:

{
    "equipment_name": "name of the equipment",
    "maintenance_jobs": [
        {
            "job_action": "type of maintenance (Inspection, Overhaul, Service, etc.)",
            "frequency": "frequency number (e.g., 12, 24, 500)",
            "frequency_type": "frequency unit (Months, Hours, Operating Hours, etc.)",
            "job_description": "detailed description of maintenance tasks",
            "section_name": "manual section if mentioned"
        }
    ]
}

Look for patterns like:
- "Every 12 months: Inspect..."
- "500 hours: Replace..."
- "Annual inspection"
- Maintenance schedules or tables

Extract all maintenance-related information from the document."""
                        ),
                        ImageURLChunk(
                            image_url=f"data:image/jpeg;base64,{base64_image}"
                        )
                    ]
                )
            ]

            # Call Mistral API
            response = self.client.chat.complete(
                model=self.model,
                messages=messages,
                max_tokens=3000
            )
            
            try:
                # Try to parse JSON response
                result = json.loads(response.choices[0].message.content)
                result["success"] = True
                result["image_path"] = str(image_path)
                return result
                
            except json.JSONDecodeError:
                # Fallback to text response
                return {
                    "equipment_name": "",
                    "maintenance_jobs": [],
                    "raw_response": response.choices[0].message.content,
                    "success": False,
                    "image_path": str(image_path),
                    "error": "Could not parse JSON response"
                }
            
        except Exception as e:
            logger.error(f"Error extracting maintenance jobs from {image_path}: {str(e)}")
            return {
                "equipment_name": "",
                "maintenance_jobs": [],
                "success": False,
                "image_path": str(image_path),
                "error": str(e)
            }
        finally:
            # Clean up temporary image if created
            if temp_image_path and temp_image_path.exists():
                try:
                    temp_image_path.unlink()
                except:
                    pass
    
    def analyze_document_structure(self, image_path: Path) -> Dict[str, Any]:
        """Analyze the overall structure and content of a document."""
        temp_image_path = None
        try:
            # Prepare image (convert PDF if necessary)
            prepared_image_path, is_temp = self._prepare_image_for_api(image_path)
            if is_temp:
                temp_image_path = prepared_image_path

            # Encode image
            base64_image = self._encode_image_to_base64(prepared_image_path)
            
            # Prepare message for document analysis
            messages = [
                UserMessage(
                    content=[
                        TextChunk(
                            text="""Analyze this document image and provide a comprehensive analysis of its structure and content.

Please return a JSON object with:

{
    "document_type": "type of document (manual, parts list, specification, etc.)",
    "title": "document title",
    "sections": [
        {
            "section_title": "section name",
            "content_type": "type of content (text, table, diagram, etc.)",
            "key_information": ["important points from this section"]
        }
    ],
    "tables": [
        {
            "table_type": "type of table (parts list, specifications, etc.)",
            "headers": ["column headers"],
            "row_count": "approximate number of rows"
        }
    ],
    "key_entities": {
        "equipment_names": ["equipment mentioned"],
        "part_numbers": ["part numbers found"],
        "specifications": ["technical specifications"]
    },
    "language": "document language",
    "quality": "document quality assessment (good/fair/poor)"
}

Provide a thorough analysis of what you can see in the document."""
                        ),
                        ImageURLChunk(
                            image_url=f"data:image/jpeg;base64,{base64_image}"
                        )
                    ]
                )
            ]

            # Call Mistral API
            response = self.client.chat.complete(
                model=self.model,
                messages=messages,
                max_tokens=3000
            )
            
            try:
                # Try to parse JSON response
                result = json.loads(response.choices[0].message.content)
                result["success"] = True
                result["image_path"] = str(image_path)
                return result
                
            except json.JSONDecodeError:
                # Fallback to text response
                return {
                    "document_type": "unknown",
                    "raw_analysis": response.choices[0].message.content,
                    "success": False,
                    "image_path": str(image_path),
                    "error": "Could not parse JSON response"
                }
            
        except Exception as e:
            logger.error(f"Error analyzing document structure for {image_path}: {str(e)}")
            return {
                "document_type": "unknown",
                "success": False,
                "image_path": str(image_path),
                "error": str(e)
            }
        finally:
            # Clean up temporary image if created
            if temp_image_path and temp_image_path.exists():
                try:
                    temp_image_path.unlink()
                except:
                    pass
