"""Excel export functionality for processing results."""

from pathlib import Path
from typing import Dict, List, Any, Optional
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from loguru import logger

from ..models.entities import ProcessingResult, Machinery, Subcomponent, SparePart, MaintenanceJob
from ..config import settings


class ExcelExporter:
    """Excel export functionality for processing results."""
    
    def __init__(self):
        self.output_dir = settings.outputs_dir
        
        # Define styling
        self.header_font = Font(bold=True, color="FFFFFF")
        self.header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        self.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    
    def export_processing_result(self, result: ProcessingResult, 
                               template_path: Optional[Path] = None) -> Path:
        """Export processing result to Excel file."""
        try:
            logger.info(f"Exporting processing result for {result.document_name}")
            
            # Generate output filename
            safe_name = "".join(c for c in result.document_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            output_filename = f"{safe_name}_extracted_data.xlsx"
            output_path = self.output_dir / output_filename
            
            if template_path and template_path.exists():
                # Use template-based export
                return self._export_with_template(result, template_path, output_path)
            else:
                # Use default export format
                return self._export_default_format(result, output_path)
                
        except Exception as e:
            logger.error(f"Error exporting to Excel: {str(e)}")
            raise
    
    def _export_default_format(self, result: ProcessingResult, output_path: Path) -> Path:
        """Export using default format with separate sheets matching manual format."""
        try:
            # Create workbook
            wb = Workbook()

            # Remove default sheet
            wb.remove(wb.active)

            # Create Components sheet (machinery/equipment)
            if result.machinery:
                self._create_components_sheet(wb, result.machinery, result.document_name)

            # Create Jobs sheet (maintenance jobs)
            if result.maintenance_jobs:
                self._create_jobs_sheet(wb, result.maintenance_jobs, result.document_name)

            # Create Spares sheet (spare parts)
            if result.spare_parts:
                self._create_spares_sheet(wb, result.spare_parts, result.document_name)

            # Save workbook
            wb.save(output_path)
            logger.info(f"Excel file exported: {output_path}")

            return output_path

        except Exception as e:
            logger.error(f"Error in default format export: {str(e)}")
            raise
    
    def _create_components_sheet(self, wb: Workbook, machinery: List[Machinery], document_name: str):
        """Create Components sheet matching manual format."""
        ws = wb.create_sheet("Components", 0)

        # Headers matching manual format
        headers = [
            "Main Machinery", "Equipment Name", "Maker Name", "Model", "Serial Number",
            "Particulars", "Motor Capacity", "Qty", "Component Type", "PDF Page No", "File Name"
        ]

        # Write headers
        for col_idx, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_idx, value=header)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = Alignment(horizontal="center")
            cell.border = self.border

        # Write data
        for row_idx, machine in enumerate(machinery, 2):
            ws.cell(row=row_idx, column=1, value=machine.name)  # Main Machinery
            ws.cell(row=row_idx, column=2, value=machine.name)  # Equipment Name
            ws.cell(row=row_idx, column=3, value=machine.manufacturer or "")  # Maker Name
            ws.cell(row=row_idx, column=4, value=machine.model or "")  # Model
            ws.cell(row=row_idx, column=5, value="")  # Serial Number
            ws.cell(row=row_idx, column=6, value=machine.description or "")  # Particulars
            ws.cell(row=row_idx, column=7, value="")  # Motor Capacity
            ws.cell(row=row_idx, column=8, value=1)  # Qty
            ws.cell(row=row_idx, column=9, value=machine.type or "")  # Component Type
            ws.cell(row=row_idx, column=10, value=machine.page_reference or "")  # PDF Page No
            ws.cell(row=row_idx, column=11, value=document_name)  # File Name

            # Apply borders
            for col_idx in range(1, len(headers) + 1):
                ws.cell(row=row_idx, column=col_idx).border = self.border

        # Auto-adjust column widths
        self._auto_adjust_columns(ws)

    def _create_jobs_sheet(self, wb: Workbook, maintenance_jobs: List[MaintenanceJob], document_name: str):
        """Create Jobs sheet matching manual format."""
        ws = wb.create_sheet("Jobs")

        # Headers matching manual format
        headers = [
            "Equipment Name", "Job Body", "Job Action", "Frequency", "Frequency Type",
            "Job Description", "Section Name", "PDF Page No", "File Name"
        ]

        # Write headers
        for col_idx, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_idx, value=header)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = Alignment(horizontal="center")
            cell.border = self.border

        # Write data
        for row_idx, job in enumerate(maintenance_jobs, 2):
            ws.cell(row=row_idx, column=1, value=job.equipment_name)  # Equipment Name
            ws.cell(row=row_idx, column=2, value=job.job_body or "")  # Job Body
            ws.cell(row=row_idx, column=3, value=job.job_action)  # Job Action
            ws.cell(row=row_idx, column=4, value=job.frequency or "")  # Frequency
            ws.cell(row=row_idx, column=5, value=job.frequency_type or "")  # Frequency Type
            ws.cell(row=row_idx, column=6, value=job.job_description)  # Job Description
            ws.cell(row=row_idx, column=7, value=job.section_name or "")  # Section Name
            ws.cell(row=row_idx, column=8, value=job.page_reference or "")  # PDF Page No
            ws.cell(row=row_idx, column=9, value=document_name)  # File Name

            # Apply borders
            for col_idx in range(1, len(headers) + 1):
                ws.cell(row=row_idx, column=col_idx).border = self.border

        # Auto-adjust column widths
        self._auto_adjust_columns(ws)

    def _create_machinery_sheet(self, wb: Workbook, machinery: List[Machinery]):
        """Create machinery sheet."""
        ws = wb.create_sheet("Machinery")
        
        # Headers
        headers = [
            "Name", "Type", "Description", "Manufacturer", "Model",
            "Specifications", "Operating Conditions", "Subcomponents",
            "Page Reference", "Confidence Score"
        ]
        
        # Write headers
        for col_idx, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_idx, value=header)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = Alignment(horizontal="center")
            cell.border = self.border
        
        # Write data
        for row_idx, machine in enumerate(machinery, 2):
            ws.cell(row=row_idx, column=1, value=machine.name)
            ws.cell(row=row_idx, column=2, value=machine.type or "")
            ws.cell(row=row_idx, column=3, value=machine.description or "")
            ws.cell(row=row_idx, column=4, value=machine.manufacturer or "")
            ws.cell(row=row_idx, column=5, value=machine.model or "")
            ws.cell(row=row_idx, column=6, value=self._dict_to_string(machine.specifications))
            ws.cell(row=row_idx, column=7, value=self._dict_to_string(machine.operating_conditions))
            ws.cell(row=row_idx, column=8, value="; ".join(machine.subcomponents))
            ws.cell(row=row_idx, column=9, value=machine.page_reference or "")
            ws.cell(row=row_idx, column=10, value=machine.confidence_score or "")
            
            # Apply borders
            for col_idx in range(1, len(headers) + 1):
                ws.cell(row=row_idx, column=col_idx).border = self.border
        
        # Auto-adjust column widths
        self._auto_adjust_columns(ws)
    
    def _create_subcomponents_sheet(self, wb: Workbook, subcomponents: List[Subcomponent]):
        """Create subcomponents sheet."""
        ws = wb.create_sheet("Subcomponents")
        
        headers = [
            "Name", "Parent Machinery", "Function", "Description", "Material",
            "Dimensions", "Parameters", "Spare Parts", "Page Reference", "Confidence Score"
        ]
        
        # Write headers
        for col_idx, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_idx, value=header)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = Alignment(horizontal="center")
            cell.border = self.border
        
        # Write data
        for row_idx, subcomp in enumerate(subcomponents, 2):
            ws.cell(row=row_idx, column=1, value=subcomp.name)
            ws.cell(row=row_idx, column=2, value=subcomp.parent_machinery or "")
            ws.cell(row=row_idx, column=3, value=subcomp.function or "")
            ws.cell(row=row_idx, column=4, value=subcomp.description or "")
            ws.cell(row=row_idx, column=5, value=subcomp.material or "")
            ws.cell(row=row_idx, column=6, value=self._dict_to_string(subcomp.dimensions))
            ws.cell(row=row_idx, column=7, value=self._dict_to_string(subcomp.parameters))
            ws.cell(row=row_idx, column=8, value="; ".join(subcomp.spare_parts))
            ws.cell(row=row_idx, column=9, value=subcomp.page_reference or "")
            ws.cell(row=row_idx, column=10, value=subcomp.confidence_score or "")
            
            # Apply borders
            for col_idx in range(1, len(headers) + 1):
                ws.cell(row=row_idx, column=col_idx).border = self.border
        
        self._auto_adjust_columns(ws)
    
    def _create_spares_sheet(self, wb: Workbook, spare_parts: List[SparePart], document_name: str):
        """Create Spares sheet matching manual format."""
        ws = wb.create_sheet("Spares")

        # Headers matching manual format
        headers = [
            "Equipment Name", "Part Name", "Part Number", "Drawing Number", "Position Number",
            "Quantity (Qty)", "Units", "Materials", "Remarks", "Spare Part Title", "PDF Page No", "File Name"
        ]

        # Write headers
        for col_idx, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_idx, value=header)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = Alignment(horizontal="center")
            cell.border = self.border

        # Write data
        for row_idx, part in enumerate(spare_parts, 2):
            ws.cell(row=row_idx, column=1, value=part.parent_subcomponent or part.parent_machinery or "Tank Cleaning Machine")  # Equipment Name
            ws.cell(row=row_idx, column=2, value=part.name)  # Part Name
            ws.cell(row=row_idx, column=3, value=part.part_number or "")  # Part Number
            ws.cell(row=row_idx, column=4, value=part.drawing_number or "")  # Drawing Number
            ws.cell(row=row_idx, column=5, value=part.position_number or "")  # Position Number
            ws.cell(row=row_idx, column=6, value=part.quantity or 1)  # Quantity (Qty)
            ws.cell(row=row_idx, column=7, value=part.units or "EA")  # Units
            ws.cell(row=row_idx, column=8, value=part.material or "")  # Materials
            ws.cell(row=row_idx, column=9, value="")  # Remarks
            ws.cell(row=row_idx, column=10, value=part.spare_part_title or "")  # Spare Part Title
            ws.cell(row=row_idx, column=11, value=part.page_reference or "")  # PDF Page No
            ws.cell(row=row_idx, column=12, value=document_name)  # File Name

            # Apply borders
            for col_idx in range(1, len(headers) + 1):
                ws.cell(row=row_idx, column=col_idx).border = self.border

        self._auto_adjust_columns(ws)
    
    def _export_with_template(self, result: ProcessingResult, 
                            template_path: Path, output_path: Path) -> Path:
        """Export using a provided Excel template."""
        try:
            logger.info(f"Using template: {template_path}")
            
            # Load template
            from openpyxl import load_workbook
            wb = load_workbook(template_path)
            
            # Map data to template sheets
            # This is a simplified implementation - would need more sophisticated mapping
            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                
                if "machinery" in sheet_name.lower():
                    self._populate_template_sheet(ws, result.machinery, "machinery")
                elif "subcomponent" in sheet_name.lower() or "component" in sheet_name.lower():
                    self._populate_template_sheet(ws, result.subcomponents, "subcomponents")
                elif "spare" in sheet_name.lower() or "part" in sheet_name.lower():
                    self._populate_template_sheet(ws, result.spare_parts, "spare_parts")
            
            wb.save(output_path)
            logger.info(f"Template-based Excel file exported: {output_path}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"Error in template-based export: {str(e)}")
            # Fallback to default format
            return self._export_default_format(result, output_path)
    
    def _populate_template_sheet(self, ws, entities: List, entity_type: str):
        """Populate a template sheet with entity data."""
        # Find the first empty row (assuming headers are in row 1)
        start_row = 2
        for row in ws.iter_rows(min_row=2, max_col=1):
            if row[0].value is None:
                break
            start_row += 1
        
        # Populate data based on entity type
        for idx, entity in enumerate(entities):
            row_idx = start_row + idx
            
            if entity_type == "machinery":
                self._populate_machinery_row(ws, row_idx, entity)
            elif entity_type == "subcomponents":
                self._populate_subcomponent_row(ws, row_idx, entity)
            elif entity_type == "spare_parts":
                self._populate_spare_part_row(ws, row_idx, entity)
    
    def _populate_machinery_row(self, ws, row_idx: int, machine: Machinery):
        """Populate a row with machinery data."""
        # This would need to be customized based on template structure
        ws.cell(row=row_idx, column=1, value=machine.name)
        ws.cell(row=row_idx, column=2, value=machine.type or "")
        ws.cell(row=row_idx, column=3, value=machine.description or "")
    
    def _populate_subcomponent_row(self, ws, row_idx: int, subcomp: Subcomponent):
        """Populate a row with subcomponent data."""
        ws.cell(row=row_idx, column=1, value=subcomp.name)
        ws.cell(row=row_idx, column=2, value=subcomp.parent_machinery or "")
        ws.cell(row=row_idx, column=3, value=subcomp.description or "")
    
    def _populate_spare_part_row(self, ws, row_idx: int, part: SparePart):
        """Populate a row with spare part data."""
        ws.cell(row=row_idx, column=1, value=part.name)
        ws.cell(row=row_idx, column=2, value=part.part_number or "")
        ws.cell(row=row_idx, column=3, value=part.parent_subcomponent or "")
    
    def _dict_to_string(self, data: Dict[str, Any]) -> str:
        """Convert dictionary to readable string."""
        if not data:
            return ""
        
        items = []
        for key, value in data.items():
            items.append(f"{key}: {value}")
        
        return "; ".join(items)
    
    def _auto_adjust_columns(self, ws):
        """Auto-adjust column widths based on content."""
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
            ws.column_dimensions[column_letter].width = adjusted_width
