"""Template management for Excel exports."""

from pathlib import Path
from typing import Dict, List, Any, Optional
import pandas as pd
from openpyxl import load_workbook, Workbook
from loguru import logger

from ..config import settings


class TemplateManager:
    """Manages Excel templates for data export."""
    
    def __init__(self):
        self.templates_dir = settings.templates_dir
        self.default_templates = {
            "shipping_standard": "shipping_standard_template.xlsx",
            "machinery_focus": "machinery_focus_template.xlsx",
            "spare_parts_catalog": "spare_parts_catalog_template.xlsx"
        }
    
    def create_default_templates(self):
        """Create default Excel templates."""
        try:
            logger.info("Creating default Excel templates")
            
            # Create shipping standard template
            self._create_shipping_standard_template()
            
            # Create machinery focus template
            self._create_machinery_focus_template()
            
            # Create spare parts catalog template
            self._create_spare_parts_catalog_template()
            
            logger.info("Default templates created successfully")
            
        except Exception as e:
            logger.error(f"Error creating default templates: {str(e)}")
            raise
    
    def _create_shipping_standard_template(self):
        """Create standard shipping industry template."""
        wb = Workbook()
        
        # Remove default sheet
        wb.remove(wb.active)
        
        # Machinery sheet
        machinery_ws = wb.create_sheet("Machinery")
        machinery_headers = [
            "Machinery Name", "Type", "Model", "Manufacturer", "Description",
            "Technical Specifications", "Operating Conditions", "Location",
            "Installation Date", "Last Maintenance", "Next Maintenance",
            "Criticality Level", "Page Reference", "Notes"
        ]
        
        for col_idx, header in enumerate(machinery_headers, 1):
            machinery_ws.cell(row=1, column=col_idx, value=header)
        
        # Subcomponents sheet
        subcomp_ws = wb.create_sheet("Subcomponents")
        subcomp_headers = [
            "Subcomponent Name", "Parent Machinery", "Function", "Type",
            "Material", "Dimensions", "Operating Parameters", "Maintenance Interval",
            "Replacement Criteria", "Safety Notes", "Page Reference", "Drawing Reference"
        ]
        
        for col_idx, header in enumerate(subcomp_headers, 1):
            subcomp_ws.cell(row=1, column=col_idx, value=header)
        
        # Spare Parts sheet
        parts_ws = wb.create_sheet("Spare Parts")
        parts_headers = [
            "Part Name", "Part Number", "Parent Subcomponent", "Parent Machinery",
            "Material", "Size/Dimensions", "Operating Pressure", "Temperature Range",
            "Manufacturer", "Supplier", "Unit Price", "Minimum Stock", "Lead Time",
            "Criticality", "Interchangeable Parts", "Page Reference", "Notes"
        ]
        
        for col_idx, header in enumerate(parts_headers, 1):
            parts_ws.cell(row=1, column=col_idx, value=header)
        
        # Maintenance Schedule sheet
        maint_ws = wb.create_sheet("Maintenance Schedule")
        maint_headers = [
            "Equipment", "Component", "Maintenance Type", "Frequency",
            "Last Performed", "Next Due", "Estimated Hours", "Required Parts",
            "Special Tools", "Safety Requirements", "Responsible Person"
        ]
        
        for col_idx, header in enumerate(maint_headers, 1):
            maint_ws.cell(row=1, column=col_idx, value=header)
        
        # Save template
        template_path = self.templates_dir / self.default_templates["shipping_standard"]
        wb.save(template_path)
        logger.info(f"Created shipping standard template: {template_path}")
    
    def _create_machinery_focus_template(self):
        """Create machinery-focused template."""
        wb = Workbook()
        wb.remove(wb.active)
        
        # Main Machinery sheet
        main_ws = wb.create_sheet("Main Machinery")
        main_headers = [
            "Equipment Name", "Type", "Model", "Serial Number", "Manufacturer",
            "Year Built", "Power Rating", "Operating Speed", "Fuel Type",
            "Cooling System", "Lubrication System", "Control System",
            "Performance Parameters", "Efficiency Rating", "Page Reference"
        ]
        
        for col_idx, header in enumerate(main_headers, 1):
            main_ws.cell(row=1, column=col_idx, value=header)
        
        # Auxiliary Machinery sheet
        aux_ws = wb.create_sheet("Auxiliary Machinery")
        aux_headers = [
            "Equipment Name", "Type", "Purpose", "Capacity", "Power Consumption",
            "Operating Pressure", "Flow Rate", "Control Method", "Backup Available",
            "Maintenance Requirements", "Page Reference"
        ]
        
        for col_idx, header in enumerate(aux_headers, 1):
            aux_ws.cell(row=1, column=col_idx, value=header)
        
        # Technical Specifications sheet
        tech_ws = wb.create_sheet("Technical Specifications")
        tech_headers = [
            "Equipment", "Parameter", "Value", "Unit", "Operating Range",
            "Design Limit", "Alarm Setpoint", "Trip Setpoint", "Measurement Method",
            "Calibration Frequency", "Page Reference"
        ]
        
        for col_idx, header in enumerate(tech_headers, 1):
            tech_ws.cell(row=1, column=col_idx, value=header)
        
        template_path = self.templates_dir / self.default_templates["machinery_focus"]
        wb.save(template_path)
        logger.info(f"Created machinery focus template: {template_path}")
    
    def _create_spare_parts_catalog_template(self):
        """Create spare parts catalog template."""
        wb = Workbook()
        wb.remove(wb.active)
        
        # Spare Parts Inventory sheet
        inventory_ws = wb.create_sheet("Spare Parts Inventory")
        inventory_headers = [
            "Part Number", "Part Name", "Description", "Equipment", "System",
            "Material", "Dimensions", "Weight", "Manufacturer", "Supplier",
            "Unit Cost", "Currency", "Minimum Stock", "Current Stock",
            "Reorder Point", "Lead Time (Days)", "Last Ordered", "Usage Rate",
            "Criticality", "Storage Location", "Storage Requirements",
            "Shelf Life", "Interchangeable With", "Page Reference", "Notes"
        ]
        
        for col_idx, header in enumerate(inventory_headers, 1):
            inventory_ws.cell(row=1, column=col_idx, value=header)
        
        # Parts by Equipment sheet
        by_equip_ws = wb.create_sheet("Parts by Equipment")
        by_equip_headers = [
            "Equipment Name", "System", "Part Number", "Part Name",
            "Quantity Required", "Replacement Frequency", "Critical Part",
            "Alternative Parts", "Supplier", "Cost", "Page Reference"
        ]
        
        for col_idx, header in enumerate(by_equip_headers, 1):
            by_equip_ws.cell(row=1, column=col_idx, value=header)
        
        # Consumables sheet
        consumables_ws = wb.create_sheet("Consumables")
        consumables_headers = [
            "Item Name", "Type", "Application", "Specification", "Brand",
            "Package Size", "Unit Cost", "Monthly Usage", "Annual Usage",
            "Supplier", "Lead Time", "Storage Requirements", "Page Reference"
        ]
        
        for col_idx, header in enumerate(consumables_headers, 1):
            consumables_ws.cell(row=1, column=col_idx, value=header)
        
        template_path = self.templates_dir / self.default_templates["spare_parts_catalog"]
        wb.save(template_path)
        logger.info(f"Created spare parts catalog template: {template_path}")
    
    def get_template_path(self, template_name: str) -> Optional[Path]:
        """Get path to a specific template."""
        if template_name in self.default_templates:
            template_path = self.templates_dir / self.default_templates[template_name]
            if template_path.exists():
                return template_path
        
        # Check for custom template
        custom_path = self.templates_dir / f"{template_name}.xlsx"
        if custom_path.exists():
            return custom_path
        
        return None
    
    def list_available_templates(self) -> List[str]:
        """List all available templates."""
        templates = []
        
        # Add default templates that exist
        for name, filename in self.default_templates.items():
            template_path = self.templates_dir / filename
            if template_path.exists():
                templates.append(name)
        
        # Add custom templates
        for template_file in self.templates_dir.glob("*.xlsx"):
            if template_file.name not in self.default_templates.values():
                templates.append(template_file.stem)
        
        return templates
    
    def analyze_template_structure(self, template_path: Path) -> Dict[str, Any]:
        """Analyze the structure of an Excel template."""
        try:
            wb = load_workbook(template_path, read_only=True)
            
            structure = {
                "filename": template_path.name,
                "sheets": {},
                "total_sheets": len(wb.sheetnames)
            }
            
            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                
                # Get headers (assume first row)
                headers = []
                for cell in ws[1]:
                    if cell.value:
                        headers.append(str(cell.value))
                    else:
                        break
                
                # Count data rows
                data_rows = 0
                for row in ws.iter_rows(min_row=2, max_col=1):
                    if row[0].value is not None:
                        data_rows += 1
                    else:
                        break
                
                structure["sheets"][sheet_name] = {
                    "headers": headers,
                    "header_count": len(headers),
                    "data_rows": data_rows,
                    "max_row": ws.max_row,
                    "max_column": ws.max_column
                }
            
            wb.close()
            return structure
            
        except Exception as e:
            logger.error(f"Error analyzing template structure: {str(e)}")
            return {"error": str(e)}
    
    def validate_template(self, template_path: Path) -> Dict[str, Any]:
        """Validate if a template is suitable for data export."""
        try:
            structure = self.analyze_template_structure(template_path)
            
            validation = {
                "valid": True,
                "warnings": [],
                "errors": [],
                "recommendations": []
            }
            
            if "error" in structure:
                validation["valid"] = False
                validation["errors"].append(f"Cannot read template: {structure['error']}")
                return validation
            
            # Check for required sheets
            sheet_names = [name.lower() for name in structure["sheets"].keys()]
            
            if not any("machinery" in name or "equipment" in name for name in sheet_names):
                validation["warnings"].append("No machinery/equipment sheet found")
            
            if not any("part" in name or "spare" in name for name in sheet_names):
                validation["warnings"].append("No spare parts sheet found")
            
            # Check for minimum headers
            for sheet_name, sheet_info in structure["sheets"].items():
                if sheet_info["header_count"] < 3:
                    validation["warnings"].append(f"Sheet '{sheet_name}' has very few headers ({sheet_info['header_count']})")
            
            # Recommendations
            if structure["total_sheets"] == 1:
                validation["recommendations"].append("Consider using multiple sheets for better organization")
            
            return validation
            
        except Exception as e:
            logger.error(f"Error validating template: {str(e)}")
            return {
                "valid": False,
                "errors": [f"Validation failed: {str(e)}"],
                "warnings": [],
                "recommendations": []
            }
