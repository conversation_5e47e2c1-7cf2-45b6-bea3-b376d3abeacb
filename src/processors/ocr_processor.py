"""OCR processing functionality."""

import os
import base64
import json
import requests
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import pytesseract
from PIL import Image
import cv2
import numpy as np
from loguru import logger

from ..config import settings

try:
    from mistralai import Mistral
    from mistralai.models.usermessage import UserMessage
    from mistralai.models.textchunk import TextChunk
    from mistralai.models.imageurlchunk import ImageURLChunk
    MISTRAL_AVAILABLE = True
except ImportError:
    MISTRAL_AVAILABLE = False
    logger.warning("Mistral AI client not available")


class OCRProcessor(ABC):
    """Abstract base class for OCR processors."""
    
    @abstractmethod
    def extract_text(self, image_path: Path) -> Tuple[str, float]:
        """Extract text from image. Returns (text, confidence)."""
        pass
    
    @abstractmethod
    def extract_text_with_boxes(self, image_path: Path) -> Dict[str, Any]:
        """Extract text with bounding boxes and metadata."""
        pass


class TesseractOCR(OCRProcessor):
    """Tesseract OCR implementation."""
    
    def __init__(self):
        # Set Tesseract command path if specified
        if settings.tesseract_cmd:
            pytesseract.pytesseract.tesseract_cmd = settings.tesseract_cmd
        
        self.languages = "+".join(settings.ocr_languages)
        
        # Test Tesseract installation
        try:
            pytesseract.get_tesseract_version()
            logger.info("Tesseract OCR initialized successfully")
        except Exception as e:
            logger.error(f"Tesseract not found or not working: {str(e)}")
            raise
    
    def preprocess_image(self, image_path: Path) -> np.ndarray:
        """Preprocess image for better OCR results."""
        # Read image
        img = cv2.imread(str(image_path))
        
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Apply denoising
        denoised = cv2.fastNlMeansDenoising(gray)
        
        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # Morphological operations to clean up
        kernel = np.ones((1, 1), np.uint8)
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    def extract_text(self, image_path: Path) -> Tuple[str, float]:
        """Extract text from image using Tesseract."""
        try:
            # Preprocess image
            processed_img = self.preprocess_image(image_path)
            
            # OCR configuration
            config = f'--oem 3 --psm 6 -l {self.languages}'
            
            # Extract text
            text = pytesseract.image_to_string(processed_img, config=config)
            
            # Get confidence scores
            data = pytesseract.image_to_data(processed_img, config=config, output_type=pytesseract.Output.DICT)
            confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
            
            return text.strip(), avg_confidence / 100.0
            
        except Exception as e:
            logger.error(f"Error in Tesseract OCR for {image_path}: {str(e)}")
            return "", 0.0
    
    def extract_text_with_boxes(self, image_path: Path) -> Dict[str, Any]:
        """Extract text with bounding boxes."""
        try:
            processed_img = self.preprocess_image(image_path)
            config = f'--oem 3 --psm 6 -l {self.languages}'
            
            # Get detailed data
            data = pytesseract.image_to_data(processed_img, config=config, output_type=pytesseract.Output.DICT)
            
            # Process results
            results = {
                "text": "",
                "words": [],
                "lines": [],
                "confidence": 0.0
            }
            
            n_boxes = len(data['text'])
            words = []
            confidences = []
            
            for i in range(n_boxes):
                if int(data['conf'][i]) > 30:  # Filter low confidence
                    word_info = {
                        "text": data['text'][i],
                        "confidence": int(data['conf'][i]) / 100.0,
                        "bbox": {
                            "x": data['left'][i],
                            "y": data['top'][i],
                            "width": data['width'][i],
                            "height": data['height'][i]
                        }
                    }
                    words.append(word_info)
                    confidences.append(int(data['conf'][i]))
            
            results["words"] = words
            results["text"] = " ".join([w["text"] for w in words])
            results["confidence"] = sum(confidences) / len(confidences) / 100.0 if confidences else 0.0
            
            return results
            
        except Exception as e:
            logger.error(f"Error extracting text with boxes: {str(e)}")
            return {"text": "", "words": [], "lines": [], "confidence": 0.0}


class TextractOCR(OCRProcessor):
    """AWS Textract OCR implementation."""
    
    def __init__(self):
        try:
            import boto3
            self.textract = boto3.client(
                'textract',
                aws_access_key_id=settings.aws_access_key_id,
                aws_secret_access_key=settings.aws_secret_access_key,
                region_name=settings.aws_region
            )
            logger.info("AWS Textract OCR initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize AWS Textract: {str(e)}")
            raise
    
    def extract_text(self, image_path: Path) -> Tuple[str, float]:
        """Extract text using AWS Textract."""
        try:
            # Read image
            with open(image_path, 'rb') as image_file:
                image_bytes = image_file.read()
            
            # Call Textract
            response = self.textract.detect_document_text(
                Document={'Bytes': image_bytes}
            )
            
            # Extract text and confidence
            text_blocks = []
            confidences = []
            
            for block in response['Blocks']:
                if block['BlockType'] == 'LINE':
                    text_blocks.append(block['Text'])
                    confidences.append(block['Confidence'])
            
            text = '\n'.join(text_blocks)
            avg_confidence = sum(confidences) / len(confidences) / 100.0 if confidences else 0.0
            
            return text, avg_confidence
            
        except Exception as e:
            logger.error(f"Error in AWS Textract OCR: {str(e)}")
            return "", 0.0
    
    def extract_text_with_boxes(self, image_path: Path) -> Dict[str, Any]:
        """Extract text with bounding boxes using Textract."""
        try:
            with open(image_path, 'rb') as image_file:
                image_bytes = image_file.read()
            
            response = self.textract.detect_document_text(
                Document={'Bytes': image_bytes}
            )
            
            results = {
                "text": "",
                "words": [],
                "lines": [],
                "confidence": 0.0
            }
            
            lines = []
            words = []
            confidences = []
            
            for block in response['Blocks']:
                if block['BlockType'] == 'WORD':
                    bbox = block['Geometry']['BoundingBox']
                    word_info = {
                        "text": block['Text'],
                        "confidence": block['Confidence'] / 100.0,
                        "bbox": {
                            "x": bbox['Left'],
                            "y": bbox['Top'],
                            "width": bbox['Width'],
                            "height": bbox['Height']
                        }
                    }
                    words.append(word_info)
                    confidences.append(block['Confidence'])
                elif block['BlockType'] == 'LINE':
                    lines.append(block['Text'])
            
            results["words"] = words
            results["lines"] = lines
            results["text"] = '\n'.join(lines)
            results["confidence"] = sum(confidences) / len(confidences) / 100.0 if confidences else 0.0
            
            return results
            
        except Exception as e:
            logger.error(f"Error in Textract text extraction with boxes: {str(e)}")
            return {"text": "", "words": [], "lines": [], "confidence": 0.0}


class VisionOCR(OCRProcessor):
    """Google Cloud Vision OCR implementation."""
    
    def __init__(self):
        try:
            from google.cloud import vision
            
            # Set credentials if provided
            if settings.google_application_credentials:
                os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = settings.google_application_credentials
            
            self.client = vision.ImageAnnotatorClient()
            logger.info("Google Cloud Vision OCR initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Google Cloud Vision: {str(e)}")
            raise
    
    def extract_text(self, image_path: Path) -> Tuple[str, float]:
        """Extract text using Google Cloud Vision."""
        try:
            from google.cloud import vision
            
            with open(image_path, 'rb') as image_file:
                content = image_file.read()
            
            image = vision.Image(content=content)
            response = self.client.text_detection(image=image)
            
            if response.error.message:
                raise Exception(f"Vision API error: {response.error.message}")
            
            texts = response.text_annotations
            if texts:
                # First annotation contains the full text
                full_text = texts[0].description
                # Confidence is not directly available in Vision API
                confidence = 0.9  # Assume high confidence for Vision API
                return full_text, confidence
            
            return "", 0.0
            
        except Exception as e:
            logger.error(f"Error in Google Vision OCR: {str(e)}")
            return "", 0.0
    
    def extract_text_with_boxes(self, image_path: Path) -> Dict[str, Any]:
        """Extract text with bounding boxes using Vision API."""
        try:
            from google.cloud import vision
            
            with open(image_path, 'rb') as image_file:
                content = image_file.read()
            
            image = vision.Image(content=content)
            response = self.client.text_detection(image=image)
            
            results = {
                "text": "",
                "words": [],
                "lines": [],
                "confidence": 0.9
            }
            
            if response.text_annotations:
                # Full text is in the first annotation
                results["text"] = response.text_annotations[0].description
                
                # Individual words/blocks in subsequent annotations
                for annotation in response.text_annotations[1:]:
                    vertices = annotation.bounding_poly.vertices
                    if vertices:
                        word_info = {
                            "text": annotation.description,
                            "confidence": 0.9,  # Vision API doesn't provide word-level confidence
                            "bbox": {
                                "x": min(v.x for v in vertices),
                                "y": min(v.y for v in vertices),
                                "width": max(v.x for v in vertices) - min(v.x for v in vertices),
                                "height": max(v.y for v in vertices) - min(v.y for v in vertices)
                            }
                        }
                        results["words"].append(word_info)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in Vision OCR with boxes: {str(e)}")
            return {"text": "", "words": [], "lines": [], "confidence": 0.0}


class MistralOCR(OCRProcessor):
    """Mistral AI OCR implementation with document understanding capabilities."""

    def __init__(self):
        if not MISTRAL_AVAILABLE:
            raise ImportError("Mistral AI client not available. Install with: pip install mistralai")

        try:
            self.client = Mistral(api_key=settings.mistral_api_key)
            self.model = getattr(settings, 'mistral_model', 'pixtral-12b-2409')
            logger.info("Mistral OCR initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Mistral OCR: {str(e)}")
            raise

    def _encode_image_to_base64(self, image_path: Path) -> str:
        """Encode image to base64 for Mistral API."""
        try:
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                return base64.b64encode(image_data).decode('utf-8')
        except Exception as e:
            logger.error(f"Error encoding image to base64: {str(e)}")
            raise

    def extract_text(self, image_path: Path) -> Tuple[str, float]:
        """Extract text from image using Mistral OCR."""
        try:
            # Encode image
            base64_image = self._encode_image_to_base64(image_path)

            # Prepare message for OCR
            messages = [
                UserMessage(
                    content=[
                        TextChunk(
                            text="Extract all text from this image. Return only the extracted text without any additional formatting or explanation."
                        ),
                        ImageURLChunk(
                            image_url=f"data:image/jpeg;base64,{base64_image}"
                        )
                    ]
                )
            ]

            # Call Mistral API
            response = self.client.chat.complete(
                model=self.model,
                messages=messages,
                max_tokens=4000
            )

            extracted_text = response.choices[0].message.content.strip()

            # Mistral doesn't provide confidence scores, so we'll use a high default
            confidence = 0.95

            return extracted_text, confidence

        except Exception as e:
            logger.error(f"Error in Mistral OCR for {image_path}: {str(e)}")
            return "", 0.0

    def extract_text_with_boxes(self, image_path: Path) -> Dict[str, Any]:
        """Extract text with structured information using Mistral."""
        try:
            # Encode image
            base64_image = self._encode_image_to_base64(image_path)

            # Prepare message for structured extraction
            messages = [
                UserMessage(
                    content=[
                        TextChunk(
                            text="""Analyze this document image and extract text with structure. Return a JSON object with:
                            {
                                "text": "full extracted text",
                                "sections": [{"title": "section title", "content": "section content"}],
                                "tables": [{"headers": ["col1", "col2"], "rows": [["val1", "val2"]]}],
                                "key_information": ["important item 1", "important item 2"]
                            }"""
                        ),
                        ImageURLChunk(
                            image_url=f"data:image/jpeg;base64,{base64_image}"
                        )
                    ]
                )
            ]

            # Call Mistral API
            response = self.client.chat.complete(
                model=self.model,
                messages=messages,
                max_tokens=4000
            )

            try:
                # Try to parse JSON response
                structured_data = json.loads(response.choices[0].message.content)

                # Convert to expected format
                results = {
                    "text": structured_data.get("text", ""),
                    "words": [],  # Mistral doesn't provide word-level boxes
                    "lines": [],
                    "confidence": 0.95,
                    "sections": structured_data.get("sections", []),
                    "tables": structured_data.get("tables", []),
                    "key_information": structured_data.get("key_information", [])
                }

                return results

            except json.JSONDecodeError:
                # Fallback to simple text extraction
                text = response.choices[0].message.content.strip()
                return {
                    "text": text,
                    "words": [],
                    "lines": text.split('\n'),
                    "confidence": 0.95,
                    "sections": [],
                    "tables": [],
                    "key_information": []
                }

        except Exception as e:
            logger.error(f"Error in Mistral OCR with boxes: {str(e)}")
            return {"text": "", "words": [], "lines": [], "confidence": 0.0}

    def extract_document_qa(self, image_path: Path, question: str) -> str:
        """Answer questions about the document using Mistral's understanding."""
        try:
            # Encode image
            base64_image = self._encode_image_to_base64(image_path)

            # Prepare message for QA
            messages = [
                UserMessage(
                    content=[
                        TextChunk(
                            text=f"Analyze this document and answer the following question: {question}\n\nProvide a detailed answer based on the information visible in the document."
                        ),
                        ImageURLChunk(
                            image_url=f"data:image/jpeg;base64,{base64_image}"
                        )
                    ]
                )
            ]

            # Call Mistral API
            response = self.client.chat.complete(
                model=self.model,
                messages=messages,
                max_tokens=2000
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Error in Mistral document QA: {str(e)}")
            return "Error processing question about the document."


def get_ocr_processor(provider: str = None) -> OCRProcessor:
    """Factory function to get OCR processor."""
    if provider is None:
        provider = settings.ocr_provider

    if provider == "tesseract":
        return TesseractOCR()
    elif provider == "textract":
        return TextractOCR()
    elif provider == "vision":
        return VisionOCR()
    elif provider == "mistral":
        return MistralOCR()
    else:
        raise ValueError(f"Unknown OCR provider: {provider}")
