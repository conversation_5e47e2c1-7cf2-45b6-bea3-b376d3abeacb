"""PDF processing functionality."""

import uuid
from pathlib import Path
from typing import Optional, Dict, Any, List
import pdfplumber
import fitz  # PyMuPDF
from loguru import logger

from ..models.document import PDFDocument, DocumentType, ProcessingStatus
from ..config import settings


class PDFProcessor:
    """Main PDF processing class."""
    
    def __init__(self):
        self.temp_dir = settings.temp_dir
        
    def load_document(self, file_path: Path) -> PDFDocument:
        """Load and analyze a PDF document."""
        try:
            # Generate unique document ID
            doc_id = str(uuid.uuid4())
            
            # Get file info
            file_size = file_path.stat().st_size
            filename = file_path.name
            
            # Open with PyMuPDF to get metadata
            pdf_doc = fitz.open(str(file_path))
            total_pages = pdf_doc.page_count
            metadata = pdf_doc.metadata
            
            # Create document model
            document = PDFDocument(
                document_id=doc_id,
                filename=filename,
                file_path=file_path,
                file_size_bytes=file_size,
                total_pages=total_pages,
                title=metadata.get('title'),
                author=metadata.get('author'),
                metadata=metadata
            )
            
            pdf_doc.close()
            logger.info(f"Loaded PDF document: {filename} ({total_pages} pages)")
            return document
            
        except Exception as e:
            logger.error(f"Error loading PDF document {file_path}: {str(e)}")
            raise
    
    def extract_text(self, document: PDFDocument) -> PDFDocument:
        """Extract text from PDF document."""
        try:
            document.update_status(ProcessingStatus.PROCESSING)
            
            # Try pdfplumber first for text extraction
            with pdfplumber.open(str(document.file_path)) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    try:
                        text = page.extract_text()
                        if text:
                            document.add_page_text(page_num, text)
                        else:
                            # Page might be scanned - mark for OCR
                            document.scanned_pages.append(page_num)
                    except Exception as e:
                        logger.warning(f"Error extracting text from page {page_num}: {str(e)}")
                        document.scanned_pages.append(page_num)
            
            # Classify pages and determine document type
            document.classify_pages()
            
            logger.info(f"Text extraction completed. Type: {document.document_type.value}")
            return document
            
        except Exception as e:
            logger.error(f"Error extracting text from document: {str(e)}")
            document.update_status(ProcessingStatus.FAILED, str(e))
            raise
    
    def extract_images_for_ocr(self, document: PDFDocument, pages: Optional[List[int]] = None) -> List[Path]:
        """Extract page images for OCR processing."""
        try:
            if pages is None:
                pages = document.scanned_pages
            
            if not pages:
                return []
            
            image_paths = []
            pdf_doc = fitz.open(str(document.file_path))
            
            for page_num in pages:
                try:
                    # Get page (0-indexed)
                    page = pdf_doc[page_num - 1]
                    
                    # Render page as image
                    mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better OCR
                    pix = page.get_pixmap(matrix=mat)
                    
                    # Save image
                    image_path = self.temp_dir / f"{document.document_id}_page_{page_num}.png"
                    pix.save(str(image_path))
                    image_paths.append(image_path)
                    
                except Exception as e:
                    logger.warning(f"Error extracting image from page {page_num}: {str(e)}")
            
            pdf_doc.close()
            logger.info(f"Extracted {len(image_paths)} page images for OCR")
            return image_paths
            
        except Exception as e:
            logger.error(f"Error extracting images for OCR: {str(e)}")
            raise
    
    def get_document_structure(self, document: PDFDocument) -> Dict[str, Any]:
        """Analyze document structure and extract sections."""
        try:
            structure = {
                "sections": [],
                "toc": [],
                "headers": [],
                "page_info": {}
            }
            
            with pdfplumber.open(str(document.file_path)) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    page_info = {
                        "page_number": page_num,
                        "width": page.width,
                        "height": page.height,
                        "text_objects": len(page.chars) if hasattr(page, 'chars') else 0
                    }
                    
                    # Extract potential headers (larger font sizes)
                    if hasattr(page, 'chars') and page.chars:
                        font_sizes = [char.get('size', 0) for char in page.chars]
                        if font_sizes:
                            avg_font_size = sum(font_sizes) / len(font_sizes)
                            large_text = [
                                char for char in page.chars 
                                if char.get('size', 0) > avg_font_size * 1.2
                            ]
                            if large_text:
                                headers = self._extract_headers_from_chars(large_text)
                                structure["headers"].extend(headers)
                    
                    structure["page_info"][page_num] = page_info
            
            return structure
            
        except Exception as e:
            logger.error(f"Error analyzing document structure: {str(e)}")
            return {"sections": [], "toc": [], "headers": [], "page_info": {}}
    
    def _extract_headers_from_chars(self, chars: List[Dict]) -> List[str]:
        """Extract potential headers from character data."""
        # Group characters by line and extract text
        lines = {}
        for char in chars:
            y = round(char.get('y0', 0))
            if y not in lines:
                lines[y] = []
            lines[y].append(char.get('text', ''))
        
        # Combine characters into lines and filter
        headers = []
        for y in sorted(lines.keys(), reverse=True):
            line_text = ''.join(lines[y]).strip()
            if line_text and len(line_text) > 3:  # Filter very short text
                headers.append(line_text)
        
        return headers
    
    def chunk_document(self, document: PDFDocument, chunk_size: int = None) -> List[Dict[str, Any]]:
        """Split document into semantic chunks."""
        if chunk_size is None:
            chunk_size = settings.chunk_size
        
        chunks = []
        
        if not document.raw_text:
            return chunks
        
        # Simple chunking by pages first
        for page_num, page_text in document.page_texts.items():
            if len(page_text) <= chunk_size:
                chunks.append({
                    "chunk_id": f"{document.document_id}_page_{page_num}",
                    "text": page_text,
                    "page_start": page_num,
                    "page_end": page_num,
                    "chunk_type": "page"
                })
            else:
                # Split large pages into smaller chunks
                words = page_text.split()
                current_chunk = []
                current_size = 0
                chunk_num = 1
                
                for word in words:
                    if current_size + len(word) > chunk_size and current_chunk:
                        chunks.append({
                            "chunk_id": f"{document.document_id}_page_{page_num}_chunk_{chunk_num}",
                            "text": " ".join(current_chunk),
                            "page_start": page_num,
                            "page_end": page_num,
                            "chunk_type": "partial_page"
                        })
                        current_chunk = [word]
                        current_size = len(word)
                        chunk_num += 1
                    else:
                        current_chunk.append(word)
                        current_size += len(word) + 1
                
                # Add remaining chunk
                if current_chunk:
                    chunks.append({
                        "chunk_id": f"{document.document_id}_page_{page_num}_chunk_{chunk_num}",
                        "text": " ".join(current_chunk),
                        "page_start": page_num,
                        "page_end": page_num,
                        "chunk_type": "partial_page"
                    })
        
        logger.info(f"Created {len(chunks)} chunks from document")
        return chunks
