"""Main application entry point for the PDF Understanding Tool."""

import sys
import argparse
from pathlib import Path
from typing import Optional, List
from loguru import logger

from .config import settings
from .agents.processing_coordinator import ProcessingCoordinator
from .exporters.excel_exporter import ExcelExporter
from .exporters.template_manager import TemplateManager
from .web.app import run_app


def setup_logging(debug: bool = False):
    """Setup logging configuration."""
    logger.remove()  # Remove default handler
    
    log_level = "DEBUG" if debug else "INFO"
    
    # Console logging
    logger.add(
        sys.stderr,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # File logging
    log_file = settings.base_dir / "logs" / "app.log"
    log_file.parent.mkdir(exist_ok=True)
    
    logger.add(
        str(log_file),
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="7 days"
    )


def process_single_file(file_path: Path, 
                       output_dir: Optional[Path] = None,
                       template_path: Optional[Path] = None,
                       use_ocr: bool = True,
                       extract_entities: bool = True) -> Path:
    """Process a single PDF file."""
    logger.info(f"Processing single file: {file_path}")
    
    if not file_path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")
    
    if not file_path.suffix.lower() == '.pdf':
        raise ValueError("Only PDF files are supported")
    
    # Initialize components
    coordinator = ProcessingCoordinator()
    exporter = ExcelExporter()
    
    # Process document
    result = coordinator.process_document(
        file_path,
        use_ocr=use_ocr,
        extract_entities=extract_entities
    )
    
    # Export to Excel
    output_path = exporter.export_processing_result(result, template_path)
    
    # Move to specified output directory if provided
    if output_dir:
        output_dir.mkdir(parents=True, exist_ok=True)
        final_path = output_dir / output_path.name
        output_path.rename(final_path)
        output_path = final_path
    
    logger.info(f"Processing completed. Output saved to: {output_path}")
    return output_path


def process_batch(input_dir: Path,
                 output_dir: Optional[Path] = None,
                 template_path: Optional[Path] = None,
                 use_ocr: bool = True,
                 extract_entities: bool = True) -> List[Path]:
    """Process all PDF files in a directory."""
    logger.info(f"Processing batch from directory: {input_dir}")
    
    if not input_dir.exists() or not input_dir.is_dir():
        raise ValueError(f"Input directory not found: {input_dir}")
    
    # Find all PDF files
    pdf_files = list(input_dir.glob("*.pdf"))
    if not pdf_files:
        raise ValueError(f"No PDF files found in {input_dir}")
    
    logger.info(f"Found {len(pdf_files)} PDF files to process")
    
    # Initialize components
    coordinator = ProcessingCoordinator()
    exporter = ExcelExporter()
    
    # Process all files
    results = coordinator.process_batch(
        pdf_files,
        use_ocr=use_ocr,
        extract_entities=extract_entities
    )
    
    # Export results
    output_paths = []
    for result in results:
        try:
            output_path = exporter.export_processing_result(result, template_path)
            
            # Move to specified output directory if provided
            if output_dir:
                output_dir.mkdir(parents=True, exist_ok=True)
                final_path = output_dir / output_path.name
                output_path.rename(final_path)
                output_path = final_path
            
            output_paths.append(output_path)
            logger.info(f"Exported: {result.document_name} -> {output_path}")
            
        except Exception as e:
            logger.error(f"Error exporting {result.document_name}: {str(e)}")
            continue
    
    logger.info(f"Batch processing completed. {len(output_paths)} files exported.")
    return output_paths


def create_templates():
    """Create default Excel templates."""
    logger.info("Creating default templates...")
    
    template_manager = TemplateManager()
    template_manager.create_default_templates()
    
    logger.info("Default templates created successfully")


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="PDF Understanding Tool for Shipping Industry",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Start web interface
  python -m src.main web
  
  # Process single file
  python -m src.main process file.pdf
  
  # Process with custom template
  python -m src.main process file.pdf --template templates/custom.xlsx
  
  # Process batch of files
  python -m src.main batch input_folder/ --output results/
  
  # Create default templates
  python -m src.main create-templates
        """
    )
    
    parser.add_argument(
        "command",
        choices=["web", "process", "batch", "create-templates"],
        help="Command to execute"
    )
    
    parser.add_argument(
        "input",
        nargs="?",
        help="Input file or directory (required for process/batch commands)"
    )
    
    parser.add_argument(
        "--output", "-o",
        type=Path,
        help="Output directory for results"
    )
    
    parser.add_argument(
        "--template", "-t",
        type=Path,
        help="Excel template file to use"
    )
    
    parser.add_argument(
        "--no-ocr",
        action="store_true",
        help="Disable OCR processing"
    )
    
    parser.add_argument(
        "--no-entities",
        action="store_true",
        help="Disable entity extraction"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )
    
    parser.add_argument(
        "--host",
        default=settings.host,
        help="Host for web interface (default: %(default)s)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=settings.port,
        help="Port for web interface (default: %(default)s)"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.debug)
    
    try:
        if args.command == "web":
            # Update settings if provided
            if args.host != settings.host:
                settings.host = args.host
            if args.port != settings.port:
                settings.port = args.port
            
            logger.info("Starting web interface...")
            run_app()
            
        elif args.command == "process":
            if not args.input:
                parser.error("Input file is required for process command")
            
            input_path = Path(args.input)
            process_single_file(
                input_path,
                output_dir=args.output,
                template_path=args.template,
                use_ocr=not args.no_ocr,
                extract_entities=not args.no_entities
            )
            
        elif args.command == "batch":
            if not args.input:
                parser.error("Input directory is required for batch command")
            
            input_dir = Path(args.input)
            process_batch(
                input_dir,
                output_dir=args.output,
                template_path=args.template,
                use_ocr=not args.no_ocr,
                extract_entities=not args.no_entities
            )
            
        elif args.command == "create-templates":
            create_templates()
            
    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        if args.debug:
            logger.exception("Full traceback:")
        sys.exit(1)


if __name__ == "__main__":
    main()
