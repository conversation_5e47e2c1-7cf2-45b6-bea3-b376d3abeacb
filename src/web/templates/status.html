{% extends "base.html" %}

{% block title %}Processing Status - PDF Understanding Tool{% endblock %}

{% block extra_css %}
<style>
    .status-card {
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .status-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .progress-ring {
        width: 120px;
        height: 120px;
        margin: 0 auto 1rem;
    }
    
    .progress-ring circle {
        fill: transparent;
        stroke-width: 8;
        stroke-linecap: round;
        transform: rotate(-90deg);
        transform-origin: 50% 50%;
    }
    
    .progress-ring .background {
        stroke: #e9ecef;
    }
    
    .progress-ring .progress {
        stroke: #007bff;
        stroke-dasharray: 283;
        stroke-dashoffset: 283;
        transition: stroke-dashoffset 0.5s ease;
    }
    
    .pulse {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-4">
                <h1 class="display-6 fw-bold">Processing Status</h1>
                <p class="text-muted">Document: <strong>{{ status.filename }}</strong></p>
            </div>
            
            <div class="card status-card">
                <div class="card-body p-5 text-center">
                    <!-- Status Icon and Progress Ring -->
                    <div id="statusDisplay">
                        <!-- Will be populated by JavaScript -->
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="progress mb-4" style="height: 10px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" 
                             style="width: {{ status.progress or 0 }}%"
                             id="progressBar">
                        </div>
                    </div>
                    
                    <!-- Status Message -->
                    <h4 id="statusMessage" class="mb-4">{{ status.message or "Processing..." }}</h4>
                    
                    <!-- Processing Details -->
                    <div class="row text-start" id="processingDetails" style="display: none;">
                        <div class="col-md-6">
                            <h6 class="text-muted">Processing Steps:</h6>
                            <ul class="list-unstyled">
                                <li id="step1"><i class="fas fa-circle text-muted me-2"></i>Document Analysis</li>
                                <li id="step2"><i class="fas fa-circle text-muted me-2"></i>Text Extraction</li>
                                <li id="step3"><i class="fas fa-circle text-muted me-2"></i>OCR Processing</li>
                                <li id="step4"><i class="fas fa-circle text-muted me-2"></i>Entity Extraction</li>
                                <li id="step5"><i class="fas fa-circle text-muted me-2"></i>Excel Generation</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Estimated Results:</h6>
                            <div class="d-flex justify-content-between">
                                <span>Machinery:</span>
                                <span class="text-primary" id="machineryCount">-</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Subcomponents:</span>
                                <span class="text-success" id="subcompCount">-</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Spare Parts:</span>
                                <span class="text-warning" id="partsCount">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="mt-4">
                        <button type="button" class="btn btn-outline-danger me-3" id="cancelBtn">
                            <i class="fas fa-stop me-2"></i>Cancel Processing
                        </button>
                        
                        <a href="#" class="btn btn-success" id="downloadBtn" style="display: none;">
                            <i class="fas fa-download me-2"></i>Download Result
                        </a>
                        
                        <a href="/upload" class="btn btn-outline-primary" id="newProcessBtn" style="display: none;">
                            <i class="fas fa-plus me-2"></i>Process Another Document
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Processing Log -->
            <div class="card mt-4" id="logCard" style="display: none;">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>Processing Log
                    </h6>
                </div>
                <div class="card-body">
                    <div id="processingLog" style="max-height: 300px; overflow-y: auto;">
                        <!-- Log entries will be added here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const processId = '{{ process_id }}';
    let pollInterval;
    let currentStatus = {{ status | tojson }};
    
    // Initialize display
    updateStatusDisplay(currentStatus);
    
    // Start polling for status updates
    startStatusPolling();
    
    // Cancel button handler
    document.getElementById('cancelBtn').addEventListener('click', cancelProcessing);
    
    function startStatusPolling() {
        pollInterval = setInterval(async () => {
            try {
                const response = await fetch(`/api/status/${processId}`);
                const status = await response.json();
                
                currentStatus = status;
                updateStatusDisplay(status);
                
                if (status.status === 'completed') {
                    clearInterval(pollInterval);
                    showCompletedState();
                } else if (status.status === 'failed' || status.status === 'cancelled') {
                    clearInterval(pollInterval);
                    showFailedState(status);
                }
                
            } catch (error) {
                console.error('Error polling status:', error);
                addLogEntry('Error checking status: ' + error.message, 'error');
            }
        }, 2000);
    }
    
    function updateStatusDisplay(status) {
        const statusDisplay = document.getElementById('statusDisplay');
        const progressBar = document.getElementById('progressBar');
        const statusMessage = document.getElementById('statusMessage');
        
        // Update progress bar
        const progress = status.progress || 0;
        progressBar.style.width = `${progress}%`;
        
        // Update status message
        statusMessage.textContent = status.message || 'Processing...';
        
        // Update status icon and progress ring
        let iconClass, iconColor, statusText;
        
        switch (status.status) {
            case 'queued':
                iconClass = 'fas fa-clock';
                iconColor = 'text-secondary';
                statusText = 'Queued';
                break;
            case 'processing':
                iconClass = 'fas fa-spinner fa-spin';
                iconColor = 'text-primary';
                statusText = 'Processing';
                document.getElementById('processingDetails').style.display = 'block';
                updateProcessingSteps(progress);
                break;
            case 'completed':
                iconClass = 'fas fa-check-circle';
                iconColor = 'text-success';
                statusText = 'Completed';
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-success');
                break;
            case 'failed':
                iconClass = 'fas fa-exclamation-circle';
                iconColor = 'text-danger';
                statusText = 'Failed';
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-danger');
                break;
            case 'cancelled':
                iconClass = 'fas fa-times-circle';
                iconColor = 'text-warning';
                statusText = 'Cancelled';
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-warning');
                break;
            default:
                iconClass = 'fas fa-question-circle';
                iconColor = 'text-muted';
                statusText = 'Unknown';
        }
        
        statusDisplay.innerHTML = `
            <div class="progress-ring">
                <svg width="120" height="120">
                    <circle class="background" cx="60" cy="60" r="45"></circle>
                    <circle class="progress" cx="60" cy="60" r="45" 
                            style="stroke-dashoffset: ${283 - (283 * progress / 100)}"></circle>
                </svg>
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                    <i class="${iconClass} ${iconColor}" style="font-size: 2rem;"></i>
                </div>
            </div>
            <h3 class="${iconColor}">${statusText}</h3>
            <p class="text-muted">${progress}% Complete</p>
        `;
        
        // Add log entry for status changes
        if (status.message) {
            addLogEntry(status.message, status.status);
        }
    }
    
    function updateProcessingSteps(progress) {
        const steps = ['step1', 'step2', 'step3', 'step4', 'step5'];
        const stepProgress = [10, 30, 50, 80, 95];
        
        steps.forEach((stepId, index) => {
            const stepElement = document.getElementById(stepId);
            const icon = stepElement.querySelector('i');
            
            if (progress >= stepProgress[index]) {
                icon.className = 'fas fa-check-circle text-success me-2';
            } else if (progress >= (stepProgress[index] - 10)) {
                icon.className = 'fas fa-spinner fa-spin text-primary me-2';
            } else {
                icon.className = 'fas fa-circle text-muted me-2';
            }
        });
    }
    
    function showCompletedState() {
        document.getElementById('cancelBtn').style.display = 'none';
        document.getElementById('downloadBtn').style.display = 'inline-block';
        document.getElementById('downloadBtn').href = `/download/${processId}`;
        document.getElementById('newProcessBtn').style.display = 'inline-block';
        
        showAlert('Processing completed successfully! You can now download the result.', 'success');
        addLogEntry('Processing completed successfully', 'success');
    }
    
    function showFailedState(status) {
        document.getElementById('cancelBtn').style.display = 'none';
        document.getElementById('newProcessBtn').style.display = 'inline-block';
        
        const message = status.status === 'cancelled' ? 'Processing was cancelled' : `Processing failed: ${status.message}`;
        const alertType = status.status === 'cancelled' ? 'warning' : 'danger';
        
        showAlert(message, alertType);
        addLogEntry(message, status.status);
    }
    
    async function cancelProcessing() {
        try {
            const response = await fetch(`/api/processing/${processId}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                clearInterval(pollInterval);
                showAlert('Processing cancelled successfully.', 'warning');
                addLogEntry('Processing cancelled by user', 'cancelled');
            } else {
                throw new Error('Failed to cancel processing');
            }
        } catch (error) {
            showAlert('Error cancelling processing: ' + error.message, 'danger');
            addLogEntry('Error cancelling processing: ' + error.message, 'error');
        }
    }
    
    function addLogEntry(message, type) {
        const logCard = document.getElementById('logCard');
        const processingLog = document.getElementById('processingLog');
        
        // Show log card if hidden
        if (logCard.style.display === 'none') {
            logCard.style.display = 'block';
        }
        
        // Determine icon and color based on type
        let icon, colorClass;
        switch (type) {
            case 'success':
            case 'completed':
                icon = 'fas fa-check-circle';
                colorClass = 'text-success';
                break;
            case 'error':
            case 'failed':
                icon = 'fas fa-exclamation-circle';
                colorClass = 'text-danger';
                break;
            case 'warning':
            case 'cancelled':
                icon = 'fas fa-exclamation-triangle';
                colorClass = 'text-warning';
                break;
            case 'processing':
                icon = 'fas fa-cog fa-spin';
                colorClass = 'text-primary';
                break;
            default:
                icon = 'fas fa-info-circle';
                colorClass = 'text-info';
        }
        
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'mb-2 pb-2 border-bottom';
        logEntry.innerHTML = `
            <div class="d-flex align-items-start">
                <i class="${icon} ${colorClass} me-2 mt-1"></i>
                <div class="flex-grow-1">
                    <div>${message}</div>
                    <small class="text-muted">${timestamp}</small>
                </div>
            </div>
        `;
        
        processingLog.appendChild(logEntry);
        
        // Scroll to bottom
        processingLog.scrollTop = processingLog.scrollHeight;
    }
});
</script>
{% endblock %}
