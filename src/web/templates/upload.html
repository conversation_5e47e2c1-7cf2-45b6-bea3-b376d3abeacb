{% extends "base.html" %}

{% block title %}Upload Document - PDF Understanding Tool{% endblock %}

{% block extra_css %}
<style>
    .upload-area {
        min-height: 300px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
    }
    
    .upload-area.dragover {
        border-color: #007bff !important;
        background-color: #e3f2fd !important;
    }
    
    .file-info {
        display: none;
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .processing-options {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        margin-top: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold">Upload PDF Document</h1>
                <p class="lead text-muted">
                    Upload your technical manual or documentation for AI-powered analysis
                </p>
            </div>
            
            <form id="uploadForm" enctype="multipart/form-data">
                <!-- File Upload Area -->
                <div class="upload-area" id="uploadArea">
                    <i class="fas fa-cloud-upload-alt fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">Drop your PDF file here</h4>
                    <p class="text-muted">or click to browse</p>
                    <input type="file" id="fileInput" name="file" accept=".pdf" style="display: none;">
                    <small class="text-muted">Maximum file size: {{ "%.0f"|format(100) }}MB</small>
                </div>
                
                <!-- File Information -->
                <div class="file-info" id="fileInfo">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-file-pdf fa-2x text-danger me-3"></i>
                        <div class="flex-grow-1">
                            <h6 class="mb-1" id="fileName"></h6>
                            <small class="text-muted" id="fileSize"></small>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="removeFile">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Processing Options -->
                <div class="processing-options">
                    <h5 class="mb-3">Processing Options</h5>
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="useOcr" name="use_ocr" checked>
                                <label class="form-check-label" for="useOcr">
                                    <strong>Enable OCR Processing</strong>
                                    <br>
                                    <small class="text-muted">
                                        Use OCR for scanned documents and images
                                    </small>
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="extractEntities" name="extract_entities" checked>
                                <label class="form-check-label" for="extractEntities">
                                    <strong>Extract Entities</strong>
                                    <br>
                                    <small class="text-muted">
                                        Extract machinery, components, and spare parts
                                    </small>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Template Selection -->
                    <div class="mt-4">
                        <label for="templateSelect" class="form-label">
                            <strong>Excel Template (Optional)</strong>
                        </label>
                        <select class="form-select" id="templateSelect" name="template_name">
                            <option value="">Use default format</option>
                            {% for template in templates %}
                            <option value="{{ template }}">{{ template|title|replace('_', ' ') }}</option>
                            {% endfor %}
                        </select>
                        <small class="form-text text-muted">
                            Select a template to format the Excel output according to your requirements.
                            <a href="/templates" target="_blank">Manage templates</a>
                        </small>
                    </div>
                </div>
                
                <!-- Submit Button -->
                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-primary btn-lg" id="submitBtn" disabled>
                        <i class="fas fa-play me-2"></i>Start Processing
                    </button>
                </div>
            </form>
            
            <!-- Processing Status (hidden initially) -->
            <div class="mt-4" id="processingStatus" style="display: none;">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-spinner fa-spin me-2"></i>Processing Document
                        </h5>
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%" id="progressBar">
                            </div>
                        </div>
                        <p class="card-text" id="statusMessage">Initializing...</p>
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-danger" id="cancelBtn">
                                <i class="fas fa-stop me-2"></i>Cancel
                            </button>
                            <a href="#" class="btn btn-success" id="downloadBtn" style="display: none;">
                                <i class="fas fa-download me-2"></i>Download Result
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const removeFileBtn = document.getElementById('removeFile');
    const submitBtn = document.getElementById('submitBtn');
    const uploadForm = document.getElementById('uploadForm');
    const processingStatus = document.getElementById('processingStatus');
    
    let selectedFile = null;
    let currentProcessId = null;
    
    // File upload handling
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);
    removeFileBtn.addEventListener('click', removeFile);
    uploadForm.addEventListener('submit', handleSubmit);
    
    function handleDragOver(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    }
    
    function handleDragLeave(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    }
    
    function handleDrop(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    }
    
    function handleFileSelect(e) {
        const files = e.target.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    }
    
    function handleFile(file) {
        // Validate file type
        if (!file.name.toLowerCase().endsWith('.pdf')) {
            showAlert('Please select a PDF file.', 'danger');
            return;
        }
        
        // Validate file size (100MB limit)
        const maxSize = 100 * 1024 * 1024;
        if (file.size > maxSize) {
            showAlert('File size exceeds 100MB limit.', 'danger');
            return;
        }
        
        selectedFile = file;
        
        // Update UI
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        fileInfo.style.display = 'block';
        uploadArea.style.display = 'none';
        submitBtn.disabled = false;
    }
    
    function removeFile() {
        selectedFile = null;
        fileInput.value = '';
        fileInfo.style.display = 'none';
        uploadArea.style.display = 'flex';
        submitBtn.disabled = true;
    }
    
    async function handleSubmit(e) {
        e.preventDefault();
        
        if (!selectedFile) {
            showAlert('Please select a file first.', 'danger');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('use_ocr', document.getElementById('useOcr').checked);
        formData.append('extract_entities', document.getElementById('extractEntities').checked);
        
        const templateSelect = document.getElementById('templateSelect');
        if (templateSelect.value) {
            formData.append('template_name', templateSelect.value);
        }
        
        try {
            // Show processing status
            uploadForm.style.display = 'none';
            processingStatus.style.display = 'block';
            
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                currentProcessId = result.process_id;
                startStatusPolling();
            } else {
                throw new Error(result.message || 'Upload failed');
            }
            
        } catch (error) {
            showAlert('Upload failed: ' + error.message, 'danger');
            uploadForm.style.display = 'block';
            processingStatus.style.display = 'none';
        }
    }
    
    function startStatusPolling() {
        const pollInterval = setInterval(async () => {
            try {
                const response = await fetch(`/api/status/${currentProcessId}`);
                const status = await response.json();
                
                updateProcessingStatus(status);
                
                if (status.status === 'completed') {
                    clearInterval(pollInterval);
                    showDownloadButton();
                } else if (status.status === 'failed' || status.status === 'cancelled') {
                    clearInterval(pollInterval);
                    showAlert(`Processing ${status.status}: ${status.message}`, 'danger');
                }
                
            } catch (error) {
                console.error('Error polling status:', error);
            }
        }, 2000);
        
        // Cancel button handler
        document.getElementById('cancelBtn').onclick = async () => {
            try {
                await fetch(`/api/processing/${currentProcessId}`, {
                    method: 'DELETE'
                });
                clearInterval(pollInterval);
                showAlert('Processing cancelled.', 'warning');
            } catch (error) {
                console.error('Error cancelling:', error);
            }
        };
    }
    
    function updateProcessingStatus(status) {
        const progressBar = document.getElementById('progressBar');
        const statusMessage = document.getElementById('statusMessage');
        
        progressBar.style.width = `${status.progress || 0}%`;
        statusMessage.textContent = status.message || 'Processing...';
        
        // Update progress bar color based on status
        progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
        if (status.status === 'completed') {
            progressBar.classList.add('bg-success');
        } else if (status.status === 'failed') {
            progressBar.classList.add('bg-danger');
        }
    }
    
    function showDownloadButton() {
        const downloadBtn = document.getElementById('downloadBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        
        downloadBtn.href = `/download/${currentProcessId}`;
        downloadBtn.style.display = 'inline-block';
        cancelBtn.style.display = 'none';
        
        showAlert('Processing completed successfully! You can now download the result.', 'success');
    }
});
</script>
{% endblock %}
