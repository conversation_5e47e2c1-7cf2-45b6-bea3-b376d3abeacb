{% extends "base.html" %}

{% block title %}Home - PDF Understanding Tool{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    AI-Powered PDF Understanding for Shipping Industry
                </h1>
                <p class="lead mb-4">
                    Automatically extract machinery, subcomponents, and spare parts information from technical manuals and documentation.
                </p>
                <div class="d-flex gap-3">
                    <a href="/upload" class="btn btn-light btn-lg">
                        <i class="fas fa-upload me-2"></i>Upload Document
                    </a>
                    <a href="#features" class="btn btn-outline-light btn-lg">
                        Learn More
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <i class="fas fa-file-pdf" style="font-size: 8rem; opacity: 0.8;"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold">Key Features</h2>
                <p class="lead text-muted">
                    Advanced AI technology designed specifically for marine engineering documentation
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-6 col-lg-3">
                <div class="card feature-card h-100 text-center p-4">
                    <div class="card-body">
                        <i class="fas fa-eye fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Smart Detection</h5>
                        <p class="card-text">
                            Automatically detects document type and determines if OCR processing is needed for scanned documents.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3">
                <div class="card feature-card h-100 text-center p-4">
                    <div class="card-body">
                        <i class="fas fa-robot fa-3x text-success mb-3"></i>
                        <h5 class="card-title">OCR Processing</h5>
                        <p class="card-text">
                            Advanced OCR with Tesseract, AWS Textract, or Google Vision for accurate text extraction from images.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3">
                <div class="card feature-card h-100 text-center p-4">
                    <div class="card-body">
                        <i class="fas fa-cogs fa-3x text-warning mb-3"></i>
                        <h5 class="card-title">Entity Extraction</h5>
                        <p class="card-text">
                            AI-powered extraction of machinery, subcomponents, and spare parts with technical specifications.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3">
                <div class="card feature-card h-100 text-center p-4">
                    <div class="card-body">
                        <i class="fas fa-file-excel fa-3x text-info mb-3"></i>
                        <h5 class="card-title">Excel Export</h5>
                        <p class="card-text">
                            Structured Excel output with customizable templates for different industry requirements.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold">How It Works</h2>
                <p class="lead text-muted">
                    Simple 4-step process to extract structured data from your technical manuals
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-6 col-lg-3">
                <div class="text-center">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <span class="fw-bold fs-4">1</span>
                    </div>
                    <h5>Upload PDF</h5>
                    <p class="text-muted">
                        Upload your technical manual or documentation in PDF format.
                    </p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3">
                <div class="text-center">
                    <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <span class="fw-bold fs-4">2</span>
                    </div>
                    <h5>AI Analysis</h5>
                    <p class="text-muted">
                        Our AI analyzes the document structure and extracts text using OCR if needed.
                    </p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3">
                <div class="text-center">
                    <div class="bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <span class="fw-bold fs-4">3</span>
                    </div>
                    <h5>Entity Extraction</h5>
                    <p class="text-muted">
                        Advanced NLP extracts machinery, components, and spare parts with specifications.
                    </p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3">
                <div class="text-center">
                    <div class="bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <span class="fw-bold fs-4">4</span>
                    </div>
                    <h5>Excel Export</h5>
                    <p class="text-muted">
                        Download structured Excel file with organized data ready for use.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Supported Formats Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-6">
                <h3 class="fw-bold mb-4">Supported Document Types</h3>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <strong>Text-based PDFs:</strong> Direct text extraction for fast processing
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <strong>Scanned PDFs:</strong> OCR processing for image-based documents
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <strong>Mixed Documents:</strong> Combination of text and scanned pages
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <strong>Technical Manuals:</strong> Engine, pump, and machinery documentation
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <strong>Parts Catalogs:</strong> Spare parts lists and specifications
                    </li>
                </ul>
            </div>
            
            <div class="col-lg-6">
                <h3 class="fw-bold mb-4">Extracted Information</h3>
                <div class="row g-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title text-primary">
                                    <i class="fas fa-cog me-2"></i>Machinery
                                </h6>
                                <small class="text-muted">
                                    Names, types, specifications, manufacturers, operating conditions
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title text-success">
                                    <i class="fas fa-puzzle-piece me-2"></i>Subcomponents
                                </h6>
                                <small class="text-muted">
                                    Component names, functions, materials, dimensions, parameters
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title text-warning">
                                    <i class="fas fa-wrench me-2"></i>Spare Parts
                                </h6>
                                <small class="text-muted">
                                    Part numbers, materials, sizes, pressures, manufacturers
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h2 class="fw-bold mb-4">Ready to Get Started?</h2>
                <p class="lead mb-4">
                    Upload your first PDF document and see the AI-powered extraction in action.
                </p>
                <a href="/upload" class="btn btn-light btn-lg">
                    <i class="fas fa-upload me-2"></i>Upload Your First Document
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}
