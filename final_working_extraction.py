#!/usr/bin/env python3
"""Final working spare parts extraction using the exact logic that works"""

import sys
from pathlib import Path
import pandas as pd
import re

# Add src to path
sys.path.insert(0, str(Path.cwd()))

from src.processors.pdf_processor import PDFProcessor
from src.exporters.excel_exporter import ExcelExporter
from src.models.entities import ProcessingResult, SparePart

def extract_all_spare_parts():
    """Extract all spare parts using the working logic."""
    
    # Load and extract text from PDF
    pdf_processor = PDFProcessor()
    document = pdf_processor.load_document(Path("example/onlyspares.pdf"))
    document = pdf_processor.extract_text(document)
    
    # Combine all page text
    full_text = ""
    for page_num, text in document.page_texts.items():
        full_text += f"\n{text}\n"
    
    lines = full_text.split('\n')
    spare_parts = []
    
    # Document-level information
    equipment_name = "Tank Cleaning Machine"
    drawing_number = "# SC 90T2-CRUDE-07"
    spare_part_title = "Spare parts breakdown SCANJET SC 90 T2 List dated 2008-07-10"
    manufacturer = "Scanjet Marine AB"
    
    print(f"Processing {len(lines)} lines...")
    
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
        
        # Look for lines that start with a number followed by space and another number
        if re.match(r'^\d+[a-z]?\s+\d+\s+', line):
            parts = line.split()
            
            if len(parts) >= 3:
                pos_candidate = parts[0]
                qty_candidate = parts[1]
                part_number = parts[2]
                description = ' '.join(parts[3:]) if len(parts) > 3 else "Spare Part"
                
                # Validate position and quantity
                if re.match(r'^\d+[a-z]?$', pos_candidate) and qty_candidate.isdigit():
                    quantity = int(qty_candidate)
                    
                    # Clean up description
                    description = re.sub(r'\s*,\s*Incl\..*$', '', description)
                    description = re.sub(r'\s*\([^)]*\)$', '', description)
                    description = re.sub(r'\s*"[^"]*"$', '', description)
                    if not description.strip():
                        description = "Spare Part"
                    
                    spare_part = SparePart(
                        name=description,
                        description=description,
                        part_number=part_number,
                        position_number=pos_candidate,
                        quantity=quantity,
                        units="EA",
                        drawing_number=drawing_number,
                        spare_part_title=spare_part_title,
                        manufacturer=manufacturer,
                        parent_machinery=equipment_name,
                        parent_subcomponent=equipment_name,
                        page_reference=1,
                        confidence_score=0.95
                    )
                    
                    spare_parts.append(spare_part)
                    print(f"Extracted: {pos_candidate} | {quantity} | {part_number} | {description}")
    
    return spare_parts

def main():
    """Main function."""
    try:
        print("Final spare parts extraction test...")
        
        # Extract spare parts
        spare_parts = extract_all_spare_parts()
        
        print(f"\nTotal spare parts extracted: {len(spare_parts)}")
        
        # Show first 10
        print("\nFirst 10 spare parts:")
        for i, part in enumerate(spare_parts[:10]):
            print(f"{i+1}. {part.name} (Part: {part.part_number}, Pos: {part.position_number}, Qty: {part.quantity})")
        
        # Create processing result
        result = ProcessingResult(
            document_id="final-test",
            document_name="onlyspares.pdf",
            total_pages=4,
            sections_processed=1,
            spare_parts=spare_parts
        )
        
        # Export to Excel
        excel_exporter = ExcelExporter()
        output_path = excel_exporter.export_processing_result(result)
        print(f"\nExcel file created: {output_path}")
        
        # Compare with expected
        expected_df = pd.read_excel("example/expectedoutput.xlsx", sheet_name='Spares')
        print(f"\nComparison:")
        print(f"Expected count: {len(expected_df)}")
        print(f"Extracted count: {len(spare_parts)}")
        
        # Check accuracy of first 10
        print(f"\nAccuracy check (first 10):")
        for i in range(min(10, len(spare_parts), len(expected_df))):
            exp_row = expected_df.iloc[i]
            ext_part = spare_parts[i]
            
            match = (exp_row['Part Name'] == ext_part.name and 
                    str(exp_row['Part Number']) == str(ext_part.part_number) and
                    str(exp_row['Position Number']) == str(ext_part.position_number))
            
            status = "✓" if match else "✗"
            print(f"{status} {i+1}: {ext_part.name} | {ext_part.part_number} | {ext_part.position_number}")
        
        return True
        
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✓ Final extraction test completed successfully")
    else:
        print("\n✗ Final extraction test failed")
        sys.exit(1)
