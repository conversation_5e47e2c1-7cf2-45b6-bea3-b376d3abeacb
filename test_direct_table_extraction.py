#!/usr/bin/env python3
"""Test direct table extraction from the PDF without chunking"""

import sys
from pathlib import Path
import pandas as pd
import re

# Add src to path
sys.path.insert(0, str(Path.cwd()))

from src.processors.pdf_processor import PDFProcessor
from src.exporters.excel_exporter import ExcelExporter
from src.models.entities import ProcessingResult, SparePart

def extract_spare_parts_directly(pdf_path: Path) -> list:
    """Extract spare parts directly from PDF text without chunking."""
    
    # Load and extract text from PDF
    pdf_processor = PDFProcessor()
    document = pdf_processor.load_document(pdf_path)
    document = pdf_processor.extract_text(document)
    
    # Combine all page text
    full_text = ""
    for page_num, text in document.page_texts.items():
        full_text += f"\n{text}\n"
    
    print("Full text extracted, parsing table...")
    
    # Extract document-level information
    equipment_name = "Tank Cleaning Machine"
    drawing_number = ""
    spare_part_title = ""
    manufacturer = ""
    
    # Look for document info
    if "# SC 90T2-CRUDE-07" in full_text:
        drawing_number = "# SC 90T2-CRUDE-07"
    if "Spare parts breakdown" in full_text:
        match = re.search(r'Spare parts breakdown[^\n]+', full_text)
        if match:
            spare_part_title = match.group(0)
    if "Scanjet Marine AB" in full_text:
        manufacturer = "Scanjet Marine AB"
    
    spare_parts_list = []
    lines = full_text.split('\n')

    print(f"Processing {len(lines)} lines...")
    processed_count = 0

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # Skip header lines and non-data lines
        skip_keywords = ['POS. NO', 'QTY.', 'SC PART NO', 'DESCRIPTION', 'NOTE!', 'FINAL',
                        'SCANJET', 'Page', 'Telephone', 'Email', 'Web', 'Göteborg', 'Sweden']
        if any(keyword in line.upper() for keyword in skip_keywords):
            print(f"Skipping header line: {line}")
            continue
        
        # Try to parse as tabular data
        parts = line.split()
        if len(parts) >= 3:
            try:
                # First part should be position number (can have letters like 43a)
                pos_candidate = parts[0]
                # Allow position numbers like 1, 43a, 133, etc.
                if not re.match(r'^\d+[a-z]?$', pos_candidate):
                    print(f"Skipping line (invalid position): {line}")
                    continue

                # Second part should be quantity (can be in parentheses or just a number)
                qty_candidate = parts[1]
                if qty_candidate.startswith('(') and qty_candidate.endswith(')'):
                    qty_candidate = qty_candidate[1:-1]

                # Check if it's a valid quantity
                if not qty_candidate.isdigit():
                    print(f"Skipping line (invalid quantity): {line}")
                    continue

                quantity = int(qty_candidate)

                # Third part should be part number - can be various formats
                part_number = parts[2]

                # Rest is description
                description = ' '.join(parts[3:]) if len(parts) > 3 else "Spare Part"

                # Clean up description (remove extra info in parentheses and "Incl." clauses)
                description = re.sub(r'\s*,\s*Incl\..*$', '', description)
                description = re.sub(r'\s*\([^)]*\)$', '', description)
                description = re.sub(r'\s*"[^"]*"$', '', description)  # Remove quoted text
                if not description.strip():
                    description = "Spare Part"
                
                spare_part = SparePart(
                    name=description,
                    description=description,
                    part_number=part_number,
                    position_number=pos_candidate,
                    quantity=quantity,
                    units="EA",
                    drawing_number=drawing_number,
                    spare_part_title=spare_part_title,
                    manufacturer=manufacturer,
                    parent_machinery=equipment_name,
                    parent_subcomponent=equipment_name,
                    page_reference="1-4",
                    confidence_score=0.95
                )
                
                spare_parts_list.append(spare_part)
                print(f"Extracted: {pos_candidate} | {quantity} | {part_number} | {description}")
                
            except (ValueError, IndexError) as e:
                continue
    
    return spare_parts_list

def main():
    """Main test function."""
    try:
        print("Testing direct table extraction...")
        
        pdf_path = Path("example/onlyspares.pdf")
        if not pdf_path.exists():
            print(f"Error: {pdf_path} not found")
            return False
        
        # Extract spare parts directly
        spare_parts = extract_spare_parts_directly(pdf_path)
        
        print(f"\nTotal spare parts extracted: {len(spare_parts)}")
        
        # Create processing result
        result = ProcessingResult(
            document_id="direct-test",
            document_name="onlyspares.pdf",
            total_pages=4,
            sections_processed=1,
            spare_parts=spare_parts
        )
        
        # Export to Excel
        excel_exporter = ExcelExporter()
        output_path = excel_exporter.export_processing_result(result)
        print(f"Excel file created: {output_path}")
        
        # Compare with expected
        expected_df = pd.read_excel("example/expectedoutput.xlsx", sheet_name='Spares')
        print(f"Expected count: {len(expected_df)}")
        print(f"Extracted count: {len(spare_parts)}")
        
        return True
        
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✓ Direct extraction test completed")
    else:
        print("\n✗ Direct extraction test failed")
        sys.exit(1)
