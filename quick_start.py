#!/usr/bin/env python3
"""
Quick start script to test and run the PDF Understanding Tool.
"""

import sys
import os
import subprocess
from pathlib import Path

def check_environment():
    """Check if the environment is properly set up."""
    print("🔍 Checking environment...")
    
    # Check if we're in the right directory
    if not Path("src").exists():
        print("❌ Error: Not in the correct directory!")
        print("Please navigate to the buildersystem directory first:")
        print("cd /path/to/buildersystem")
        return False
    
    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  .env file not found. Creating from template...")
        env_example = Path(".env.example")
        if env_example.exists():
            env_file.write_text(env_example.read_text())
            print("✅ .env file created from .env.example")
            print("📝 Please edit .env and add your OpenAI API key!")
        else:
            print("❌ .env.example not found!")
            return False
    
    # Check for OpenAI API key
    try:
        from dotenv import load_dotenv
        load_dotenv()
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key or api_key.startswith("your_"):
            print("⚠️  OpenAI API key not set in .env file!")
            print("Please edit .env and add your actual API key:")
            print("OPENAI_API_KEY=sk-your-actual-api-key-here")
            return False
        else:
            print("✅ OpenAI API key found")
    except ImportError:
        print("⚠️  python-dotenv not installed, skipping API key check")
    
    print("✅ Environment check passed!")
    return True

def test_basic_imports():
    """Test if basic imports work."""
    print("\n🧪 Testing basic imports...")
    
    required_modules = [
        "fastapi",
        "uvicorn", 
        "pdfplumber",
        "pandas",
        "openpyxl"
    ]
    
    failed = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module} - {e}")
            failed.append(module)
    
    if failed:
        print(f"\n❌ Missing required modules: {failed}")
        print("Please install them with:")
        print("pip install " + " ".join(failed))
        return False
    
    print("✅ All basic imports successful!")
    return True

def start_application():
    """Start the application."""
    print("\n🚀 Starting the PDF Understanding Tool...")
    
    try:
        # Try to import and run the app
        sys.path.insert(0, str(Path.cwd()))
        
        print("Starting web server...")
        print("📱 Web interface will be available at: http://localhost:8000")
        print("📚 API documentation at: http://localhost:8000/docs")
        print("\n🛑 Press Ctrl+C to stop the server")
        print("-" * 50)
        
        # Run the application
        if Path("run.py").exists():
            subprocess.run([sys.executable, "run.py"], check=True)
        else:
            subprocess.run([sys.executable, "-m", "src.main", "web"], check=True)
            
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Error starting application: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure all dependencies are installed: pip install -r requirements-minimal.txt")
        print("2. Check that your .env file has the correct API key")
        print("3. Try running: python test_installation.py")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False
    
    return True

def show_usage_instructions():
    """Show how to use the application."""
    print("\n📖 How to use the PDF Understanding Tool:")
    print("1. 📁 Upload a PDF document (technical manual, parts catalog, etc.)")
    print("2. ⚙️  Configure processing options:")
    print("   - Enable OCR for scanned documents")
    print("   - Choose entity extraction")
    print("   - Select Excel template (optional)")
    print("3. 🔄 Wait for processing to complete")
    print("4. 📥 Download the structured Excel result")
    print("\n🎯 The tool will extract:")
    print("   • Machinery information (engines, pumps, etc.)")
    print("   • Subcomponents (bearings, seals, valves)")
    print("   • Spare parts (with part numbers and specifications)")

def main():
    """Main function."""
    print("=" * 60)
    print("🚢 PDF Understanding Tool for Shipping Industry")
    print("=" * 60)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed!")
        print("Please fix the issues above and try again.")
        return False
    
    # Test imports
    if not test_basic_imports():
        print("\n❌ Import test failed!")
        print("Please install missing dependencies and try again.")
        return False
    
    # Show usage instructions
    show_usage_instructions()
    
    # Ask user if they want to start
    print("\n" + "=" * 60)
    response = input("🚀 Ready to start the application? (y/n): ").lower().strip()
    
    if response in ['y', 'yes', '']:
        return start_application()
    else:
        print("👋 Goodbye! Run this script again when you're ready.")
        return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
