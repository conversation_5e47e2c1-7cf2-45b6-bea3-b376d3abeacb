#!/usr/bin/env python3
"""Debug script to examine the Excel output and understand the data extraction issue."""

import pandas as pd
import sys
from pathlib import Path

def examine_excel_file(file_path):
    """Examine the Excel file to see what data was actually extracted."""
    try:
        print(f"Examining Excel file: {file_path}")
        
        # Read all sheets
        excel_file = pd.ExcelFile(file_path)
        print(f"Available sheets: {excel_file.sheet_names}")
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n=== Sheet: {sheet_name} ===")
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            print(f"Shape: {df.shape}")
            print(f"Columns: {list(df.columns)}")
            
            if not df.empty:
                print("\nFirst 5 rows:")
                print(df.head())
                
                # Check for "Unknown" values
                unknown_count = 0
                for col in df.columns:
                    if df[col].dtype == 'object':  # String columns
                        unknown_in_col = (df[col] == 'Unknown').sum()
                        if unknown_in_col > 0:
                            print(f"Column '{col}' has {unknown_in_col} 'Unknown' values")
                            unknown_count += unknown_in_col
                
                print(f"Total 'Unknown' values in sheet: {unknown_count}")
                
                # Show some non-unknown values if they exist
                for col in df.columns:
                    if df[col].dtype == 'object':
                        non_unknown = df[df[col] != 'Unknown'][col].dropna()
                        if not non_unknown.empty:
                            print(f"\nSample values from '{col}':")
                            print(non_unknown.head(3).tolist())
            else:
                print("Sheet is empty")
    
    except Exception as e:
        print(f"Error examining Excel file: {e}")

if __name__ == "__main__":
    # Find the Excel file
    outputs_dir = Path("outputs")
    excel_files = list(outputs_dir.glob("*.xlsx"))
    
    if excel_files:
        latest_file = max(excel_files, key=lambda x: x.stat().st_mtime)
        examine_excel_file(latest_file)
    else:
        print("No Excel files found in outputs directory")
