#!/usr/bin/env python3
"""Analyze the manually created Tank Cleaning Machine.xlsx file to understand the structure."""

import pandas as pd
import sys
from pathlib import Path

def analyze_manual_excel():
    """Analyze the manual Excel file structure."""
    file_path = "Tank Cleaning Machine.xlsx"
    
    if not Path(file_path).exists():
        print(f"File {file_path} not found!")
        return
    
    try:
        print(f"Analyzing manual Excel file: {file_path}")
        
        # Read all sheets
        excel_file = pd.ExcelFile(file_path)
        print(f"Available sheets: {excel_file.sheet_names}")
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n{'='*60}")
            print(f"Sheet: {sheet_name}")
            print('='*60)
            
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            print(f"Shape: {df.shape}")
            print(f"Columns: {list(df.columns)}")
            
            if not df.empty:
                print(f"\nFirst 5 rows:")
                print(df.head())
                
                print(f"\nColumn data types:")
                for col in df.columns:
                    print(f"  {col}: {df[col].dtype}")
                
                # Show some sample data for each column
                print(f"\nSample data:")
                for col in df.columns:
                    non_null_values = df[col].dropna()
                    if not non_null_values.empty:
                        sample_values = non_null_values.head(3).tolist()
                        print(f"  {col}: {sample_values}")
                    else:
                        print(f"  {col}: [No data]")
            else:
                print("Sheet is empty")
                
        print(f"\n{'='*60}")
        print("Analysis complete!")
        
    except Exception as e:
        print(f"Error analyzing Excel file: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_manual_excel()
