#!/usr/bin/env python3
"""Debug script to test LLM response parsing."""

import json
import re
from typing import List, Dict

def test_json_parsing():
    """Test different JSON response formats that might come from the LLM."""
    
    # Sample responses that might come from the LLM
    sample_responses = [
        # Good JSON format
        '''[
            {
                "name": "Main Engine",
                "type": "Engine",
                "description": "Primary propulsion engine"
            },
            {
                "name": "Fuel Pump",
                "type": "Pump",
                "description": "Delivers fuel to engine"
            }
        ]''',
        
        # JSON with extra text
        '''Here are the machinery items I found:
        
        [
            {
                "name": "Turbine",
                "type": "Engine", 
                "description": "Component used for generating power"
            }
        ]
        
        These are the main machinery components.''',
        
        # Malformed JSON that might cause issues
        '''[
            {
                "name": "SPARE PART KIT",
                "part_number": null,
                "subcomponent": "Maintenance",
                "material": null,
            }
        ]''',
        
        # JSON with markdown formatting
        '''```json
        [
            {
                "name": "Tank Cleaning Machine",
                "type": "Cleaning Equipment",
                "description": "Used for cleaning cargo tanks"
            }
        ]
        ```'''
    ]
    
    def parse_json_response(response_text: str) -> List[Dict]:
        """Parse JSON response from LLM, with fallback handling."""
        print(f"\n=== Testing Response ===")
        print(f"Input: {response_text[:100]}...")
        
        try:
            # Try to extract JSON from response
            json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                print(f"Extracted JSON: {json_str[:100]}...")
                result = json.loads(json_str)
                print(f"Successfully parsed: {len(result)} items")
                return result
            else:
                # Try parsing the entire response
                result = json.loads(response_text)
                print(f"Parsed entire response: {len(result)} items")
                return result
                
        except json.JSONDecodeError as e:
            print(f"JSON parsing failed: {e}")
            print("Using fallback extraction...")
            return fallback_entity_extraction(response_text)
    
    def fallback_entity_extraction(text: str) -> List[Dict]:
        """Fallback entity extraction using regex patterns."""
        entities = []
        
        # Simple pattern-based extraction
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if len(line) > 10 and any(char.isalnum() for char in line):
                entities.append({
                    "name": line[:50],  # Truncate long names
                    "description": "",
                    "confidence": 0.3  # Low confidence for fallback
                })
        
        return entities[:10]  # Limit to 10 entities
    
    # Test each sample response
    for i, response in enumerate(sample_responses):
        print(f"\n{'='*50}")
        print(f"Test {i+1}")
        result = parse_json_response(response)
        print(f"Result: {result}")

if __name__ == "__main__":
    test_json_parsing()
